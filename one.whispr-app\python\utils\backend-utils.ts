import { spawn, exec } from 'child_process';
import path from 'path';
import fs from 'fs';
import ora from 'ora';

// Configuration
export const REPO_INFO = {
  owner: 'indygreg',
  repo: 'python-build-standalone',
  tag: '20240107',
  version: 'cpython-3.11.7'
};

// Pinned Python requirements with tested compatible versions
export const REQUIREMENTS = [
  // Core dependencies
  'pyperclip==1.9.0',
  'keyboard==0.13.5',
  'sounddevice==0.5.2',
  'PyAudioWPatch==********',
  'numpy==1.26.4',                   // Using stable numpy 1.x for compatibility
  
  // Speech recognition
  'transformers[torch]==4.45.0',     // Stable version with good torch compatibility
  'optimum==1.22.0',
  'accelerate==0.34.0',
  
  'jellyfish==1.1.0',
  'protobuf==4.25.5',                // Compatible with transformers
  
  // Speaker diarization
  'pyannote-audio==3.3.1',
  
  // Audio processing
  'scipy==1.11.4',                   // Compatible with numpy 1.26.4
  'pydub==0.25.1',
  'silero-vad==5.1.0',
  
  // Windows screen capture & OCR context system
  'mss==9.0.1',                      // Stable version
  'pywin32==306',                    // Stable version
  'easyocr==1.7.1',                  // Compatible with opencv
  'opencv-python==*********',        // Stable version
  
  // Image processing (already included for OCR)
  'pillow==10.4.0',                  // Stable version
  
  // Improved logging
  'rich==13.8.1',                    // Stable version
  'logging-formatter-anticrlf==1.2.1',
  'pynvml==11.5.3',                  // Stable version
  
  // Communication
  'websockets==12.0',                // Stable version
  'aiohttp==3.10.10',                // Stable version
  'aiofiles==24.1.0',
  
  // Other
  'pyinstaller==6.10.0',             // Stable version
  'setuptools==69.5.1'               // Known stable version < 81 that works well
];

// Special torch requirements with CUDA support (stable versions)
export const TORCH_REQUIREMENTS = {
  packages: ['torch==2.4.1', 'torchvision==0.19.1', 'torchaudio==2.4.1'],
  indexUrl: 'https://download.pytorch.org/whl/cu121'
};

export const PLATFORM_ASSETS = {
  win32: {
    x64: `${REPO_INFO.version}+${REPO_INFO.tag}-x86_64-pc-windows-msvc-shared-install_only.tar.gz`,
    arm64: `${REPO_INFO.version}+${REPO_INFO.tag}-aarch64-pc-windows-msvc-shared-install_only.tar.gz`
  },
  darwin: {
    x64: `${REPO_INFO.version}+${REPO_INFO.tag}-x86_64-apple-darwin-install_only.tar.gz`,
    arm64: `${REPO_INFO.version}+${REPO_INFO.tag}-aarch64-apple-darwin-install_only.tar.gz`
  },
  linux: {
    x64: `${REPO_INFO.version}+${REPO_INFO.tag}-x86_64-unknown-linux-gnu-install_only.tar.gz`,
    arm64: `${REPO_INFO.version}+${REPO_INFO.tag}-aarch64-unknown-linux-gnu-install_only.tar.gz`
  }
};

// Custom error class for better error handling
export class PythonProcessError extends Error {
  exitCode: number;
  stdout: string;
  stderr: string;

  constructor(message: string, exitCode: number, stdout: string, stderr: string) {
    super(message);
    this.name = 'PythonProcessError';
    this.exitCode = exitCode;
    this.stdout = stdout;
    this.stderr = stderr;
  }
}

// Get paths in a cross-platform way
export function getPaths() {
  const projectRoot = process.cwd();
  const distDir = path.join(projectRoot, '.dist');
  const pythonDir = path.join(distDir, 'python');
  
  // Platform-specific paths
  const pythonExePath = process.platform === 'win32'
    ? path.join(pythonDir, 'python.exe')
    : path.join(pythonDir, 'bin', 'python3');
    
  const sitePackagesPath = process.platform === 'win32'
    ? path.join(pythonDir, 'Lib', 'site-packages')
    : path.join(pythonDir, 'lib', 'python3.11', 'site-packages');
  
  return {
    projectRoot,
    distDir,
    pythonDir,
    pythonExePath,
    sitePackagesPath,
    outputDir: path.join(distDir, 'python-exe'),
    buildDir: path.join(distDir, 'python-exe', 'build'),
    specFilePath: path.join(projectRoot, 'python', 'backend.fresh.spec'),
    finalOutputDir: path.join(distDir, 'One Whispr Backend')
  };
}

// Helper function to run commands
export async function runCommand(command: string, args: string[], options: any = {}): Promise<string> {
  return new Promise((resolve, reject) => {
    const proc = spawn(command, args, {
      stdio: 'pipe',
      shell: process.platform === 'win32', // Use shell on Windows
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    if (proc.stdout) {
      proc.stdout.on('data', (data) => {
        stdout += data.toString();
      });
    }
    
    if (proc.stderr) {
      proc.stderr.on('data', (data) => {
        stderr += data.toString();
      });
    }
    
    proc.on('error', (error) => {
      reject(new Error(`Failed to run command: ${error.message}`));
    });
    
    proc.on('close', (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        reject(new PythonProcessError(`Command exited with code ${code}`, code || 1, stdout, stderr));
      }
    });
  });
}

// Run Python process with detailed output handling
export async function runPythonProcess(
  pythonExePath: string, 
  args: string[], 
  options: any = {}
): Promise<{stdout: string, stderr: string, exitCode: number}> {
  return new Promise((resolve) => {
    const proc = spawn(pythonExePath, args, {
      stdio: 'pipe',
      shell: process.platform === 'win32',
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    if (proc.stdout) {
      proc.stdout.on('data', (data) => {
        const text = data.toString();
        stdout += text;
        // Optionally print to console if callback is provided
        if (options.onStdout) {
          options.onStdout(text);
        }
      });
    }
    
    if (proc.stderr) {
      proc.stderr.on('data', (data) => {
        const text = data.toString();
        stderr += text;
        if (options.onStderr) {
          options.onStderr(text);
        }
      });
    }
    
    proc.on('error', (error) => {
      resolve({
        stdout,
        stderr: stderr + `\nProcess error: ${error.message}`,
        exitCode: 1
      });
    });
    
    proc.on('close', (code) => {
      resolve({
        stdout,
        stderr,
        exitCode: code || 0
      });
    });
  });
}

// Get torch requirements for special installation
export function getTorchRequirements(): { packages: string[], indexUrl: string } {
  return TORCH_REQUIREMENTS;
}

// Check which packages are already installed
export async function getInstalledPackages(pythonExePath: string, sitePackagesPath: string): Promise<Map<string, string>> {
  try {
    const output = await runCommand(pythonExePath, [
      '-m', 'pip', 'list', '--format=json'
    ], {
      env: {
        ...process.env,
        PYTHONPATH: sitePackagesPath
      }
    });
    
    const packages = new Map<string, string>();
    const parsedOutput = JSON.parse(output);
    
    for (const pkg of parsedOutput) {
      // Normalize package names to handle different casing and formats
      const normalizedName = pkg.name.toLowerCase().replace(/[-_]/g, '-');
      packages.set(normalizedName, pkg.version);
      // Also store original name for debugging
      packages.set(pkg.name.toLowerCase(), pkg.version);
    }
    
    return packages;
  } catch (error) {
    console.warn('Warning: Could not get installed packages list:', error);
    // If pip list fails, return empty map
    return new Map<string, string>();
  }
}

// Parse package name and version from requirement string
export function parsePackageSpec(requirement: string): { name: string, version: string | null, specifier: string | null } {
  // Normalize the requirement string
  const normalized = requirement.trim();
  
  // Handle complex requirements like package[extra]==version
  const extraMatch = normalized.match(/^([a-zA-Z0-9_.-]+)(\[[a-zA-Z0-9_,.-]+\])?(.*)$/);
  if (!extraMatch) {
    return { name: normalized.toLowerCase(), version: null, specifier: null };
  }
  
  // Get the base package name without any version specifiers
  const baseName = extraMatch[1];
  
  // Normalize package name to handle pip naming conventions
  // pip normalizes package names by converting underscores to hyphens and lowercasing
  const normalizedName = baseName.toLowerCase().replace(/[-_]/g, '-');
  
  // If there are extras (like [torch]), include them in the name
  // pip considers 'transformers' and 'transformers[torch]' as the same package
  // We should match them by base name only
  const versionSpec = extraMatch[3] || '';
  
  // Extract version and specifier if present
  // Updated regex to handle version patterns like 4.52.4, ********, etc.
  const versionMatch = versionSpec.match(/([=<>~!]{1,2})([\d.]+)/);
  const version = versionMatch ? versionMatch[2] : null;
  const specifier = versionMatch ? versionMatch[1] : null;
  
  return { 
    // For package comparison, we use the normalized base name without extras
    name: normalizedName, 
    version,
    specifier
  };
}

// Check if PyInstaller is installed
export async function checkPyInstaller(pythonExePath: string): Promise<boolean> {
  try {
    await runCommand(pythonExePath, ['-m', 'PyInstaller', '--version']);
    return true;
  } catch (error) {
    return false;
  }
}

// Helper function to recursively copy a folder
export function copyFolderRecursiveSync(source: string, destination: string) {
  // Check if source exists
  if (!fs.existsSync(source)) {
    throw new Error(`Source directory not found: ${source}`);
  }
  
  const stats = fs.statSync(source);
  const isDirectory = stats.isDirectory();
  
  if (isDirectory) {
    // Create destination folder if it doesn't exist
    const destFolder = path.join(destination, path.basename(source));
    if (!fs.existsSync(destFolder)) {
      fs.mkdirSync(destFolder, { recursive: true });
    }
    
    // Copy all files in the directory
    fs.readdirSync(source).forEach(childItemName => {
      copyFolderRecursiveSync(
        path.join(source, childItemName),
        destFolder
      );
    });
  } else {
    // Copy file
    fs.copyFileSync(source, path.join(destination, path.basename(source)));
  }
}

// Helper function to count files in a directory recursively
export function countFiles(dir: string): number {
  let count = 0;
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const itemPath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      count += countFiles(itemPath);
    } else {
      count++;
    }
  }
  
  return count;
}

// Preserve site-packages directory
export function preserveSitePackages(sitePackagesPath: string): string | null {
  const { distDir } = getPaths();
  
  if (fs.existsSync(sitePackagesPath)) {
    const tempDir = path.join(distDir, 'temp-site-packages');
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    fs.mkdirSync(tempDir, { recursive: true });
    fs.cpSync(sitePackagesPath, tempDir, { recursive: true });
    return tempDir;
  }
  
  return null;
}

// Restore site-packages from a temporary directory
export function restoreSitePackages(tempDir: string | null, sitePackagesPath: string): void {
  if (tempDir && fs.existsSync(tempDir)) {
    if (!fs.existsSync(sitePackagesPath)) {
      fs.mkdirSync(sitePackagesPath, { recursive: true });
    }
    fs.cpSync(tempDir, sitePackagesPath, { recursive: true });
    fs.rmSync(tempDir, { recursive: true, force: true });
  }
}

// Kill any running Python processes on Windows
export async function killPythonProcesses(spinner?: ora.Ora): Promise<void> {
  if (process.platform === 'win32') {
    if (spinner) spinner.text = '🔄 Ensuring no Python processes are running...';
    try {
      await new Promise<void>((resolve) => {
        exec('taskkill /f /im python.exe /im pythonw.exe /im python3.exe', () => {
          // Ignore errors - the process might not exist
          resolve();
        });
      });
      
      // Add a delay to ensure processes are fully terminated
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      // Ignore errors - the process might not exist
    }
  }
} 