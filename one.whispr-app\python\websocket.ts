/**
 * WebSocket connection management for backend communication.
 */

import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { IPCMessage } from './types';
import { isBackendRunning } from './process';

// WebSocket state
let wsClient: WebSocket | null = null;
let wsPort: number | null = null;
let isConnected = false;
let reconnectAttempts = 0;
const maxReconnectAttempts = 10;
const reconnectDelay = 1000; // 1 second

// Export event emitter for backend events
export const backendEvents = new EventEmitter();

/**
 * Handle a WebSocket message
 */
function handleWebSocketMessage(message: IPCMessage): void {
  if (message.type === 'event') {
    // This is an event message - emit both specific event and as general message
    backendEvents.emit(message.event!, message.data);
    
    // Also emit as 'message' so the event forwarding system picks it up
    backendEvents.emit('message', message);
  } else if (message.type === 'rule_usage_update') {
    // Handle rule usage updates specifically
    backendEvents.emit('rule_usage_update', message);
    backendEvents.emit('message', message);
  } else if (message.type === 'server_welcome') {
    // Server welcome message
    console.log('Received server welcome message', message.args);
    backendEvents.emit('server_welcome', message.args);
  } else {
    // Unknown message or response - emit as message for other handlers
    console.log('Received WebSocket message:', message);
    backendEvents.emit('message', message);
  }
}

/**
 * Try to reconnect to the WebSocket server
 */
function tryReconnect(): void {
  if (reconnectAttempts >= maxReconnectAttempts) {
    console.error('Maximum reconnect attempts reached');
    backendEvents.emit('reconnect_failed');
    return;
  }
  
  reconnectAttempts++;
  console.log(`Attempting to reconnect (attempt ${reconnectAttempts}/${maxReconnectAttempts})...`);
  
  setTimeout(() => {
    if (wsPort !== null) {
      connectToWebSocket(wsPort)
        .catch((error) => {
          console.error('Reconnect attempt failed:', error);
        });
    }
  }, reconnectDelay * reconnectAttempts);
}

/**
 * Connect to the WebSocket server
 */
export function connectToWebSocket(port: number): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      if (wsClient) {
        wsClient.terminate();
        wsClient = null;
      }
      
      wsPort = port;
      wsClient = new WebSocket(`ws://127.0.0.1:${port}`);
      
      wsClient.on('open', () => {
        console.log(`Connected to backend WebSocket server on port ${port}`);
        isConnected = true;
        reconnectAttempts = 0;
        backendEvents.emit('connected');
        resolve();
      });
      
      wsClient.on('message', (data: WebSocket.Data) => {
        try {
          const message = JSON.parse(data.toString());
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      });
      
      wsClient.on('close', () => {
        console.log('WebSocket connection closed');
        isConnected = false;
        backendEvents.emit('disconnected');
        
        if (isBackendRunning()) {
          tryReconnect();
        }
      });
      
      wsClient.on('error', (error) => {
        console.error('WebSocket error:', error);
        isConnected = false;
        if (reconnectAttempts === 0) {
          reject(error);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Send a raw message through the WebSocket
 */
export function sendWebSocketMessage(message: IPCMessage): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!isConnected || !wsClient) {
      reject(new Error('WebSocket not connected'));
      return;
    }
    
    wsClient.send(JSON.stringify(message), (error) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
}

/**
 * Close the WebSocket connection
 */
export function closeWebSocket(): void {
  if (wsClient) {
    try {
      wsClient.terminate();
    } catch (error) {
      console.error('Error closing WebSocket:', error);
    }
    wsClient = null;
  }
  
  isConnected = false;
  wsPort = null;
  reconnectAttempts = 0;
}

/**
 * Check if connected to the WebSocket server
 */
export function isWebSocketConnected(): boolean {
  return isConnected;
}

/**
 * Get the WebSocket port
 */
export function getWebSocketPort(): number | null {
  return wsPort;
} 