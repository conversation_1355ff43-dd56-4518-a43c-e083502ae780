"""
OCR text extraction functionality for context awareness.

Uses EasyOCR with GPU acceleration and optimized settings for speed and accuracy.
"""

import logging
import re
import time
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import numpy as np
import easyocr


class OCRExtractor:
    """Manages OCR text extraction from images."""
    
    def __init__(self):
        """Initialize the OCR extractor."""
        self.logger = logging.getLogger("whispr.context.ocr")
        self._reader = None
        self._languages = ['en']  # Default to English
        self._confidence_threshold = 0.5
        self._initialized = False
        self._gpu_available = False
        
    def initialize(self, languages: Optional[List[str]] = None, confidence_threshold: float = 0.5) -> bool:
        """Initialize the OCR system with GPU acceleration.

        Args:
            languages: List of language codes (e.g., ['en', 'es', 'fr'])
            confidence_threshold: Minimum confidence threshold for text detection (0.0 to 1.0)

        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            if languages:
                self._languages = languages
            self._confidence_threshold = confidence_threshold

            self.logger.info(f"Initializing EasyOCR with languages: {self._languages}")

            # Try GPU first for maximum speed
            try:
                self._reader = easyocr.Reader(
                    self._languages,
                    gpu=True,
                    model_storage_directory=None,  # Use default
                    download_enabled=True,
                    detector=True,
                    recognizer=True,
                    verbose=False
                )
                self._gpu_available = True
                self.logger.info("EasyOCR initialized with GPU acceleration")
            except Exception as gpu_error:
                self.logger.warning(f"GPU initialization failed: {gpu_error}")
                # Fallback to CPU
                try:
                    self._reader = easyocr.Reader(
                        self._languages,
                        gpu=False,
                        model_storage_directory=None,
                        download_enabled=True,
                        detector=True,
                        recognizer=True,
                        verbose=False
                    )
                    self._gpu_available = False
                    self.logger.info("EasyOCR initialized with CPU (GPU not available)")
                except Exception as cpu_error:
                    self.logger.error(f"CPU initialization also failed: {cpu_error}")
                    return False

            self._initialized = True
            self.logger.info("EasyOCR initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize EasyOCR: {e}")
            return False
    
    def extract_text(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text from an image using EasyOCR.

        Args:
            image: PIL Image object to extract text from

        Returns:
            Dictionary containing extracted text and metadata
        """
        if not self._initialized or not self._reader:
            self.logger.error("OCR extractor not initialized")
            return {"text": "", "words": [], "confidence": 0.0}

        try:
            # Convert PIL Image to numpy array
            image_array = np.array(image)

            # Log image properties for debugging
            self.logger.debug(f"Processing image: {image.size}, mode: {image.mode}, GPU: {self._gpu_available}")

            # Early exit for very large images that would be slow
            total_pixels = image_array.shape[0] * image_array.shape[1]
            if total_pixels > 2000000:  # ~2 megapixels max for good performance
                self.logger.debug(f"Image too large ({total_pixels} pixels), skipping OCR")
                return {"text": "", "words": [], "confidence": 0.0}

            # Perform OCR with optimized settings for speed and accuracy
            ocr_start_time = time.time()

            # Optimized EasyOCR settings for best speed/accuracy balance
            results = self._reader.readtext(
                image_array,
                # Text detection parameters (balanced for speed/accuracy)
                width_ths=0.5,          # Balanced threshold
                height_ths=0.5,         # Balanced threshold
                text_threshold=0.6,     # Higher threshold for better accuracy
                low_text=0.3,           # Reasonable threshold
                link_threshold=0.3,     # Reasonable threshold

                # Performance parameters
                canvas_size=2048,       # Good balance of speed/quality
                mag_ratio=1.0,          # No magnification for speed

                # Output parameters
                paragraph=False,        # Disable for speed
                detail=1,               # Include bounding boxes and confidence

                # Batch processing (if multiple images)
                batch_size=1,

                # Additional optimizations
                allowlist=None,         # Allow all characters
                blocklist=None,         # No character blocking
                decoder='greedy',       # Faster decoder
                beamWidth=5,           # Balanced beam width

                # GPU-specific optimizations
                workers=0 if self._gpu_available else 1,  # Let GPU handle parallelism
            )

            ocr_time = time.time() - ocr_start_time
            self.logger.debug(f"EasyOCR completed in {ocr_time:.3f}s, found {len(results)} text elements")

            # Process results with proper error handling
            extracted_data = self._process_easyocr_results(results)

            return extracted_data

        except Exception as e:
            self.logger.error(f"Error extracting text from image: {e}")
            return {"text": "", "words": [], "confidence": 0.0}
    

    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text.
        
        Args:
            text: Raw text from OCR
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
            
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove very short "words" that are likely OCR noise
        if len(text) < 2:
            return ""
            
        # Remove text that's mostly special characters
        if len(re.sub(r'[^a-zA-Z0-9\s]', '', text)) < len(text) * 0.3:
            return ""
            
        return text
    
    def _process_easyocr_results(self, results: List) -> Dict[str, Any]:
        """Process EasyOCR results into structured data.

        Args:
            results: Raw OCR results from EasyOCR

        Returns:
            Processed text data with confidence filtering
        """
        words = []
        all_text = []
        total_confidence = 0.0
        valid_results = 0
        rejected_count = 0

        self.logger.debug(f"Processing {len(results)} EasyOCR results with confidence threshold {self._confidence_threshold}")

        for result in results:
            try:
                # EasyOCR returns results as [bbox, text, confidence]
                if isinstance(result, (list, tuple)) and len(result) >= 3:
                    bbox, text, confidence = result[0], result[1], result[2]
                elif isinstance(result, (list, tuple)) and len(result) == 2:
                    # Some versions might return [text, confidence]
                    text, confidence = result[0], result[1]
                    bbox = None
                else:
                    self.logger.debug(f"Unexpected EasyOCR result format: {result}")
                    rejected_count += 1
                    continue

                # Ensure text is string and clean it
                text = str(text).strip()

                # Ensure confidence is float
                confidence = float(confidence)

                # Filter by confidence threshold and text length
                if confidence >= self._confidence_threshold and len(text) > 0:
                    # Clean up the text
                    cleaned_text = self._clean_text(text)

                    if cleaned_text:  # Only include non-empty text
                        words.append({
                            "text": cleaned_text,
                            "confidence": confidence,
                            "bbox": bbox
                        })
                        all_text.append(cleaned_text)
                        total_confidence += confidence
                        valid_results += 1
                else:
                    rejected_count += 1

            except Exception as e:
                self.logger.debug(f"Error processing EasyOCR result {result}: {e}")
                rejected_count += 1
                continue

        # Log processing results
        if rejected_count > 0:
            self.logger.debug(f"Rejected {rejected_count} low confidence/invalid elements")

        if words:
            sample_kept = [f"'{w['text']}' ({w['confidence']:.2f})" for w in words[:5]]
            self.logger.debug(f"Kept {len(words)} valid elements. Samples: {', '.join(sample_kept)}")
        else:
            self.logger.debug("No valid text elements found")

        # Calculate average confidence
        avg_confidence = total_confidence / valid_results if valid_results > 0 else 0.0

        # Join all text with spaces
        full_text = " ".join(all_text)

        return {
            "text": full_text,
            "words": words,
            "confidence": avg_confidence,
            "word_count": len(words)
        }

    def extract_keywords(self, text: str, min_length: int = 3) -> List[str]:
        """Extract potential keywords from text.
        
        Args:
            text: Text to extract keywords from
            min_length: Minimum length for keywords
            
        Returns:
            List of potential keywords
        """
        if not text:
            return []
            
        try:
            # Split into words and filter
            words = re.findall(r'\b[a-zA-Z0-9]+\b', text.lower())
            
            # Filter by length and common stop words
            stop_words = {
                'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
                'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
                'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those',
                'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
                'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
                'can', 'must', 'shall', 'a', 'an', 'as', 'if', 'when', 'where', 'why',
                'how', 'what', 'which', 'who', 'whom', 'whose', 'all', 'any', 'both',
                'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor',
                'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very'
            }
            
            keywords = []
            for word in words:
                if (len(word) >= min_length and 
                    word not in stop_words and 
                    not word.isdigit()):
                    keywords.append(word)
            
            # Remove duplicates while preserving order
            unique_keywords = []
            seen = set()
            for keyword in keywords:
                if keyword not in seen:
                    unique_keywords.append(keyword)
                    seen.add(keyword)
            
            return unique_keywords[:20]  # Limit to top 20 keywords
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords: {e}")
            return []
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """Set the confidence threshold for text detection.
        
        Args:
            threshold: New confidence threshold (0.0 to 1.0)
        """
        if 0.0 <= threshold <= 1.0:
            self._confidence_threshold = threshold
            self.logger.debug(f"OCR confidence threshold set to {threshold}")
        else:
            self.logger.warning(f"Invalid confidence threshold: {threshold}")
    
    def set_languages(self, languages: List[str]) -> bool:
        """Set the languages for OCR detection.

        Args:
            languages: List of language codes

        Returns:
            True if languages were set successfully, False otherwise
        """
        try:
            if languages != self._languages:
                self._languages = languages
                self.logger.info(f"EasyOCR languages updated to: {languages}")
                # Reinitialize reader with new languages for immediate effect
                if self._initialized:
                    self.logger.info("Reinitializing EasyOCR with new languages...")
                    gpu_mode = self._gpu_available
                    self._reader = easyocr.Reader(
                        self._languages,
                        gpu=gpu_mode,
                        verbose=False
                    )
                    self.logger.info("EasyOCR reinitialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error setting EasyOCR languages: {e}")
            return False
    
    def is_initialized(self) -> bool:
        """Check if the OCR extractor is initialized.

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized and self._reader is not None
    




    def cleanup(self) -> None:
        """Clean up OCR resources."""
        self._reader = None
        self._initialized = False
        self._gpu_available = False
        self.logger.debug("EasyOCR extractor resources cleaned up")
