"""
OCR text extraction functionality for context awareness.

Uses Windows OCR API (faster) with fallback to pytesseract for better performance.
"""

import logging
import re
import time
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import numpy as np


class OCRExtractor:
    """Manages OCR text extraction from images."""
    
    def __init__(self):
        """Initialize the OCR extractor."""
        self.logger = logging.getLogger("whispr.context.ocr")
        self._ocr_method = None
        self._languages = ['en']  # Default to English
        self._confidence_threshold = 0.5
        self._initialized = False
        self._use_windows_ocr = True  # Try Windows OCR first
        
    def initialize(self, languages: Optional[List[str]] = None, confidence_threshold: float = 0.5) -> bool:
        """Initialize the OCR system.

        Args:
            languages: List of language codes (e.g., ['en', 'es', 'fr'])
            confidence_threshold: Minimum confidence threshold for text detection (0.0 to 1.0)

        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            if languages:
                self._languages = languages
            self._confidence_threshold = confidence_threshold

            # For now, disable OCR and focus on window-based context analysis
            # This provides excellent context for transcription without the complexity of OCR
            self._ocr_method = "disabled"
            self.logger.info("OCR disabled - context awareness using window titles and application detection")
            self._initialized = True
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize any OCR method: {e}")
            return False
    
    def extract_text(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text from an image.

        Args:
            image: PIL Image object to extract text from

        Returns:
            Dictionary containing extracted text and metadata
        """
        if not self._initialized:
            self.logger.error("OCR extractor not initialized")
            return {"text": "", "words": [], "confidence": 0.0}

        # OCR is currently disabled for performance and reliability
        # Context awareness will rely on window titles and application detection
        self.logger.debug("OCR disabled, returning empty text data")
        return {"text": "", "words": [], "confidence": 0.0}
    

    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text.
        
        Args:
            text: Raw text from OCR
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
            
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove very short "words" that are likely OCR noise
        if len(text) < 2:
            return ""
            
        # Remove text that's mostly special characters
        if len(re.sub(r'[^a-zA-Z0-9\s]', '', text)) < len(text) * 0.3:
            return ""
            
        return text
    
    def extract_keywords(self, text: str, min_length: int = 3) -> List[str]:
        """Extract potential keywords from text.
        
        Args:
            text: Text to extract keywords from
            min_length: Minimum length for keywords
            
        Returns:
            List of potential keywords
        """
        if not text:
            return []
            
        try:
            # Split into words and filter
            words = re.findall(r'\b[a-zA-Z0-9]+\b', text.lower())
            
            # Filter by length and common stop words
            stop_words = {
                'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
                'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
                'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those',
                'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
                'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
                'can', 'must', 'shall', 'a', 'an', 'as', 'if', 'when', 'where', 'why',
                'how', 'what', 'which', 'who', 'whom', 'whose', 'all', 'any', 'both',
                'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor',
                'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very'
            }
            
            keywords = []
            for word in words:
                if (len(word) >= min_length and 
                    word not in stop_words and 
                    not word.isdigit()):
                    keywords.append(word)
            
            # Remove duplicates while preserving order
            unique_keywords = []
            seen = set()
            for keyword in keywords:
                if keyword not in seen:
                    unique_keywords.append(keyword)
                    seen.add(keyword)
            
            return unique_keywords[:20]  # Limit to top 20 keywords
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords: {e}")
            return []
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """Set the confidence threshold for text detection.
        
        Args:
            threshold: New confidence threshold (0.0 to 1.0)
        """
        if 0.0 <= threshold <= 1.0:
            self._confidence_threshold = threshold
            self.logger.debug(f"OCR confidence threshold set to {threshold}")
        else:
            self.logger.warning(f"Invalid confidence threshold: {threshold}")
    
    def set_languages(self, languages: List[str]) -> bool:
        """Set the languages for OCR detection.

        Args:
            languages: List of language codes

        Returns:
            True if languages were set successfully, False otherwise
        """
        try:
            if languages != self._languages:
                self._languages = languages
                self.logger.info(f"OCR languages updated to: {languages}")
                # Note: Language switching would require reinitializing the OCR engine
                # For now, we'll just update the setting
            return True
        except Exception as e:
            self.logger.error(f"Error setting OCR languages: {e}")
            return False
    
    def is_initialized(self) -> bool:
        """Check if the OCR extractor is initialized.

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized and self._ocr_method is not None
    




    def cleanup(self) -> None:
        """Clean up OCR resources."""
        self._ocr_method = None
        self._initialized = False
        self.logger.debug("OCR extractor resources cleaned up")
