"""
OCR text extraction functionality for context awareness.

Uses EasyOCR with GPU acceleration and optimized settings for speed and accuracy.
"""

import logging
import re
import time
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import numpy as np
import easyocr


class OCRExtractor:
    """Manages OCR text extraction from images."""
    
    def __init__(self):
        """Initialize the OCR extractor."""
        self.logger = logging.getLogger("whispr.context.ocr")
        self._reader = None
        self._languages = ['en']  # Default to English
        self._confidence_threshold = 0.5
        self._initialized = False
        self._gpu_available = False
        
    def initialize(self, languages: Optional[List[str]] = None, confidence_threshold: float = 0.5) -> bool:
        """Initialize the OCR system with GPU acceleration.

        Args:
            languages: List of language codes (e.g., ['en', 'es', 'fr'])
            confidence_threshold: Minimum confidence threshold for text detection (0.0 to 1.0)

        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            if languages:
                self._languages = languages
            self._confidence_threshold = confidence_threshold

            self.logger.info(f"Initializing EasyOCR with languages: {self._languages}")

            # Try GPU first for maximum speed
            try:
                self._reader = easyocr.Reader(
                    self._languages,
                    gpu=True,
                    model_storage_directory=None,  # Use default
                    download_enabled=True,
                    detector=True,
                    recognizer=True,
                    verbose=False
                )
                self._gpu_available = True
                self.logger.info("EasyOCR initialized with GPU acceleration")
            except Exception as gpu_error:
                self.logger.warning(f"GPU initialization failed: {gpu_error}")
                # Fallback to CPU
                try:
                    self._reader = easyocr.Reader(
                        self._languages,
                        gpu=False,
                        model_storage_directory=None,
                        download_enabled=True,
                        detector=True,
                        recognizer=True,
                        verbose=False
                    )
                    self._gpu_available = False
                    self.logger.info("EasyOCR initialized with CPU (GPU not available)")
                except Exception as cpu_error:
                    self.logger.error(f"CPU initialization also failed: {cpu_error}")
                    return False

            self._initialized = True
            self.logger.info("EasyOCR initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize EasyOCR: {e}")
            return False
    
    def extract_text(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text from an image using EasyOCR.

        Args:
            image: PIL Image object to extract text from

        Returns:
            Dictionary containing extracted text and metadata
        """
        if not self._initialized or not self._reader:
            self.logger.error("OCR extractor not initialized")
            return {"text": "", "words": [], "confidence": 0.0}

        try:
            # Convert PIL Image to numpy array
            image_array = np.array(image)

            # Log image properties for debugging
            self.logger.debug(f"Processing image: {image.size}, mode: {image.mode}, GPU: {self._gpu_available}")

            # Check if we should use tiled processing for large images
            total_pixels = image_array.shape[0] * image_array.shape[1]
            if total_pixels > 800000:  # ~0.8 megapixels - use tiled processing
                self.logger.debug(f"Large image ({total_pixels} pixels), using tiled batch processing")
                return self._extract_text_tiled(image_array)

            # For smaller images, use standard processing
            self.logger.debug(f"Standard processing for image: {total_pixels} pixels")

            # Perform OCR with optimized settings and timeout protection
            ocr_start_time = time.time()

            try:
                # Optimized EasyOCR settings for standard processing
                results = self._reader.readtext(
                    image_array,
                    # Balanced thresholds for quality/speed
                    width_ths=0.6,          # Balanced threshold
                    height_ths=0.6,         # Balanced threshold
                    text_threshold=0.6,     # Balanced threshold
                    low_text=0.3,           # Balanced threshold
                    link_threshold=0.3,     # Balanced threshold

                    # Optimal canvas size for standard images
                    canvas_size=1536,       # Good balance for standard processing
                    mag_ratio=1.0,          # No magnification

                    # Optimizations
                    paragraph=False,        # Disable for speed
                    detail=1,               # Include confidence and bbox
                    batch_size=1,           # Single image processing

                    # Decoder settings
                    decoder='greedy',       # Fast decoder
                    beamWidth=5,           # Balanced beam width

                    # Worker optimization
                    workers=0 if self._gpu_available else 1,
                )

                ocr_time = time.time() - ocr_start_time

                # Log performance with warnings for slow processing
                if ocr_time > 3.0:
                    self.logger.warning(f"OCR took {ocr_time:.2f}s - consider image optimization")
                else:
                    self.logger.debug(f"EasyOCR completed in {ocr_time:.3f}s, found {len(results)} text elements")

            except Exception as ocr_error:
                ocr_time = time.time() - ocr_start_time
                self.logger.error(f"OCR processing failed after {ocr_time:.2f}s: {ocr_error}")
                return {"text": "", "words": [], "confidence": 0.0}

            # Process results with proper error handling
            extracted_data = self._process_easyocr_results(results)

            return extracted_data

        except Exception as e:
            self.logger.error(f"Error extracting text from image: {e}")
            return {"text": "", "words": [], "confidence": 0.0}
    

    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text.
        
        Args:
            text: Raw text from OCR
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
            
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove very short "words" that are likely OCR noise
        if len(text) < 2:
            return ""
            
        # Remove text that's mostly special characters
        if len(re.sub(r'[^a-zA-Z0-9\s]', '', text)) < len(text) * 0.3:
            return ""
            
        return text
    
    def _process_easyocr_results(self, results: List) -> Dict[str, Any]:
        """Process EasyOCR results into structured data.

        Args:
            results: Raw OCR results from EasyOCR

        Returns:
            Processed text data with confidence filtering
        """
        words = []
        all_text = []
        total_confidence = 0.0
        valid_results = 0
        rejected_count = 0

        self.logger.debug(f"Processing {len(results)} EasyOCR results with confidence threshold {self._confidence_threshold}")

        for result in results:
            try:
                # EasyOCR returns results as [bbox, text, confidence]
                if isinstance(result, (list, tuple)) and len(result) >= 3:
                    bbox, text, confidence = result[0], result[1], result[2]
                elif isinstance(result, (list, tuple)) and len(result) == 2:
                    # Some versions might return [text, confidence]
                    text, confidence = result[0], result[1]
                    bbox = None
                else:
                    self.logger.debug(f"Unexpected EasyOCR result format: {result}")
                    rejected_count += 1
                    continue

                # Ensure text is string and clean it
                text = str(text).strip()

                # Ensure confidence is float
                confidence = float(confidence)

                # Filter by confidence threshold and text length
                if confidence >= self._confidence_threshold and len(text) > 0:
                    # Clean up the text
                    cleaned_text = self._clean_text(text)

                    if cleaned_text:  # Only include non-empty text
                        words.append({
                            "text": cleaned_text,
                            "confidence": confidence,
                            "bbox": bbox
                        })
                        all_text.append(cleaned_text)
                        total_confidence += confidence
                        valid_results += 1
                else:
                    rejected_count += 1

            except Exception as e:
                self.logger.debug(f"Error processing EasyOCR result {result}: {e}")
                rejected_count += 1
                continue

        # Log processing results
        if rejected_count > 0:
            self.logger.debug(f"Rejected {rejected_count} low confidence/invalid elements")

        if words:
            sample_kept = [f"'{w['text']}' ({w['confidence']:.2f})" for w in words[:5]]
            self.logger.debug(f"Kept {len(words)} valid elements. Samples: {', '.join(sample_kept)}")
        else:
            self.logger.debug("No valid text elements found")

        # Calculate average confidence
        avg_confidence = total_confidence / valid_results if valid_results > 0 else 0.0

        # Join all text with spaces
        full_text = " ".join(all_text)

        return {
            "text": full_text,
            "words": words,
            "confidence": avg_confidence,
            "word_count": len(words)
        }

    def extract_keywords(self, text: str, min_length: int = 3) -> List[str]:
        """Extract potential keywords from text.
        
        Args:
            text: Text to extract keywords from
            min_length: Minimum length for keywords
            
        Returns:
            List of potential keywords
        """
        if not text:
            return []
            
        try:
            # Split into words and filter
            words = re.findall(r'\b[a-zA-Z0-9]+\b', text.lower())
            
            # Filter by length and common stop words
            stop_words = {
                'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
                'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
                'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those',
                'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
                'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
                'can', 'must', 'shall', 'a', 'an', 'as', 'if', 'when', 'where', 'why',
                'how', 'what', 'which', 'who', 'whom', 'whose', 'all', 'any', 'both',
                'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor',
                'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very'
            }
            
            keywords = []
            for word in words:
                if (len(word) >= min_length and 
                    word not in stop_words and 
                    not word.isdigit()):
                    keywords.append(word)
            
            # Remove duplicates while preserving order
            unique_keywords = []
            seen = set()
            for keyword in keywords:
                if keyword not in seen:
                    unique_keywords.append(keyword)
                    seen.add(keyword)
            
            return unique_keywords[:20]  # Limit to top 20 keywords
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords: {e}")
            return []
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """Set the confidence threshold for text detection.
        
        Args:
            threshold: New confidence threshold (0.0 to 1.0)
        """
        if 0.0 <= threshold <= 1.0:
            self._confidence_threshold = threshold
            self.logger.debug(f"OCR confidence threshold set to {threshold}")
        else:
            self.logger.warning(f"Invalid confidence threshold: {threshold}")
    
    def set_languages(self, languages: List[str]) -> bool:
        """Set the languages for OCR detection.

        Args:
            languages: List of language codes

        Returns:
            True if languages were set successfully, False otherwise
        """
        try:
            if languages != self._languages:
                self._languages = languages
                self.logger.info(f"EasyOCR languages updated to: {languages}")
                # Reinitialize reader with new languages for immediate effect
                if self._initialized:
                    self.logger.info("Reinitializing EasyOCR with new languages...")
                    gpu_mode = self._gpu_available
                    self._reader = easyocr.Reader(
                        self._languages,
                        gpu=gpu_mode,
                        verbose=False
                    )
                    self.logger.info("EasyOCR reinitialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error setting EasyOCR languages: {e}")
            return False
    
    def is_initialized(self) -> bool:
        """Check if the OCR extractor is initialized.

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized and self._reader is not None
    




    def _extract_text_tiled(self, image_array: np.ndarray) -> Dict[str, Any]:
        """Extract text using tiled batch processing for large images.

        Args:
            image_array: Large numpy array to process in tiles

        Returns:
            Combined OCR results from all tiles
        """
        try:
            height, width = image_array.shape[:2]

            # Calculate optimal tile size based on image size and GPU availability
            if self._gpu_available:
                tile_size = 512  # Larger tiles for GPU processing
                max_tiles = 16   # GPU can handle more tiles
            else:
                tile_size = 400  # Smaller tiles for CPU processing
                max_tiles = 8    # Limit tiles for CPU

            # Calculate number of tiles
            tiles_x = max(1, min(width // tile_size, int(max_tiles ** 0.5)))
            tiles_y = max(1, min(height // tile_size, int(max_tiles ** 0.5)))

            # Adjust tile size to cover the entire image
            actual_tile_width = width // tiles_x
            actual_tile_height = height // tiles_y

            self.logger.debug(f"Processing {tiles_x}x{tiles_y} tiles of size {actual_tile_width}x{actual_tile_height}")

            # Create tiles with smart prioritization
            tiles = []
            tile_positions = []
            tile_priorities = []

            for y in range(tiles_y):
                for x in range(tiles_x):
                    start_x = x * actual_tile_width
                    start_y = y * actual_tile_height
                    end_x = min(start_x + actual_tile_width, width)
                    end_y = min(start_y + actual_tile_height, height)

                    # Extract tile
                    tile = image_array[start_y:end_y, start_x:end_x]

                    # Skip very small tiles
                    if tile.shape[0] < 50 or tile.shape[1] < 50:
                        continue

                    # Calculate tile priority (top tiles are more important)
                    priority = self._calculate_tile_priority(y, x, tiles_y, tiles_x)

                    tiles.append(tile)
                    tile_positions.append((start_x, start_y))
                    tile_priorities.append(priority)

            # Sort tiles by priority (process most important first)
            if tiles:
                sorted_indices = sorted(range(len(tiles)), key=lambda i: tile_priorities[i], reverse=True)
                tiles = [tiles[i] for i in sorted_indices]
                tile_positions = [tile_positions[i] for i in sorted_indices]

            if not tiles:
                self.logger.debug("No valid tiles found")
                return {"text": "", "words": [], "confidence": 0.0}

            # Process tiles in batches for optimal GPU utilization
            batch_size = 4 if self._gpu_available else 2
            all_results = []

            ocr_start_time = time.time()

            for i in range(0, len(tiles), batch_size):
                batch_tiles = tiles[i:i + batch_size]
                batch_positions = tile_positions[i:i + batch_size]

                # Process batch
                batch_results = self._process_tile_batch(batch_tiles, batch_positions)
                all_results.extend(batch_results)

            ocr_time = time.time() - ocr_start_time
            self.logger.debug(f"Tiled OCR completed in {ocr_time:.3f}s, processed {len(tiles)} tiles")

            # Combine results from all tiles
            return self._combine_tile_results(all_results)

        except Exception as e:
            self.logger.error(f"Error in tiled OCR processing: {e}")
            return {"text": "", "words": [], "confidence": 0.0}

    def _process_tile_batch(self, tiles: List[np.ndarray], positions: List[Tuple[int, int]]) -> List[Dict[str, Any]]:
        """Process a batch of tiles with EasyOCR.

        Args:
            tiles: List of tile image arrays
            positions: List of (x, y) positions for each tile

        Returns:
            List of OCR results for each tile
        """
        batch_results = []

        try:
            # Process each tile in the batch
            for tile, (offset_x, offset_y) in zip(tiles, positions):
                try:
                    # Use faster settings for tiles
                    results = self._reader.readtext(
                        tile,
                        width_ths=0.8,          # Higher threshold for speed
                        height_ths=0.8,
                        text_threshold=0.8,
                        low_text=0.5,
                        link_threshold=0.5,
                        canvas_size=512,        # Small canvas for tiles
                        mag_ratio=1.0,
                        paragraph=False,
                        detail=1,
                        decoder='greedy',
                        beamWidth=3,
                        workers=0 if self._gpu_available else 1,
                    )

                    # Adjust bounding boxes to global coordinates
                    adjusted_results = []
                    for result in results:
                        if len(result) >= 3:
                            bbox, text, confidence = result[0], result[1], result[2]

                            # Adjust bbox coordinates
                            if bbox is not None:
                                adjusted_bbox = []
                                for point in bbox:
                                    adjusted_bbox.append([point[0] + offset_x, point[1] + offset_y])
                                bbox = adjusted_bbox

                            adjusted_results.append([bbox, text, confidence])

                    batch_results.append({
                        "results": adjusted_results,
                        "tile_position": (offset_x, offset_y),
                        "tile_size": tile.shape
                    })

                except Exception as tile_error:
                    self.logger.debug(f"Error processing tile at {offset_x},{offset_y}: {tile_error}")
                    batch_results.append({
                        "results": [],
                        "tile_position": (offset_x, offset_y),
                        "tile_size": tile.shape
                    })

        except Exception as e:
            self.logger.error(f"Error processing tile batch: {e}")

        return batch_results

    def _combine_tile_results(self, tile_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Combine OCR results from multiple tiles.

        Args:
            tile_results: List of tile OCR results

        Returns:
            Combined OCR results
        """
        all_results = []

        # Flatten all tile results
        for tile_result in tile_results:
            all_results.extend(tile_result.get("results", []))

        # Process combined results
        return self._process_easyocr_results(all_results)

    def _calculate_tile_priority(self, tile_y: int, tile_x: int, total_y: int, total_x: int) -> float:
        """Calculate priority for a tile based on its position.

        Args:
            tile_y: Y position of tile
            tile_x: X position of tile
            total_y: Total number of Y tiles
            total_x: Total number of X tiles

        Returns:
            Priority score (higher = more important)
        """
        # Top tiles are most important (title bars, current content)
        y_priority = (total_y - tile_y) / total_y

        # Center-left tiles are more important than edges
        x_center = total_x / 2
        x_distance = abs(tile_x - x_center) / x_center
        x_priority = 1.0 - (x_distance * 0.3)  # Slight preference for center

        # Top-left quadrant gets highest priority
        if tile_y < total_y / 2 and tile_x < total_x / 2:
            quadrant_bonus = 0.2
        else:
            quadrant_bonus = 0.0

        return y_priority * 0.7 + x_priority * 0.3 + quadrant_bonus

    def _looks_like_terminal_content(self, image: Image.Image) -> bool:
        """Check if image looks like terminal/editor content with lots of small text.

        Args:
            image: PIL Image to analyze

        Returns:
            True if image appears to be terminal-like content
        """
        try:
            # Convert to grayscale for analysis
            gray_image = image.convert('L')
            image_array = np.array(gray_image)

            # Calculate text density by looking at pixel variation
            # Terminal content typically has high contrast and lots of small text

            # Look for high contrast patterns (text on background)
            edges = np.abs(np.diff(image_array, axis=1))
            high_contrast_pixels = np.sum(edges > 50)  # Threshold for text edges
            total_pixels = image_array.size

            contrast_ratio = high_contrast_pixels / total_pixels

            # Terminal content typically has >5% high-contrast pixels
            is_text_heavy = contrast_ratio > 0.05

            if is_text_heavy:
                self.logger.debug(f"High text density detected: {contrast_ratio:.3f}")

            return is_text_heavy

        except Exception as e:
            self.logger.debug(f"Error analyzing image content: {e}")
            return False

    def cleanup(self) -> None:
        """Clean up OCR resources."""
        self._reader = None
        self._initialized = False
        self._gpu_available = False
        self.logger.debug("EasyOCR extractor resources cleaned up")
