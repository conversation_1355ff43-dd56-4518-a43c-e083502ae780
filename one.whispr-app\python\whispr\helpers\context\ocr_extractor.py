"""
OCR text extraction functionality for context awareness.

Uses Windows OCR API (faster) with fallback to pytesseract for better performance.
"""

import logging
import re
import time
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import numpy as np


class OCRExtractor:
    """Manages OCR text extraction from images."""
    
    def __init__(self):
        """Initialize the OCR extractor."""
        self.logger = logging.getLogger("whispr.context.ocr")
        self._ocr_method = None
        self._languages = ['en']  # Default to English
        self._confidence_threshold = 0.5
        self._initialized = False
        self._use_windows_ocr = True  # Try Windows OCR first
        
    def initialize(self, languages: Optional[List[str]] = None, confidence_threshold: float = 0.5) -> bool:
        """Initialize the OCR system.

        Args:
            languages: List of language codes (e.g., ['en', 'es', 'fr'])
            confidence_threshold: Minimum confidence threshold for text detection (0.0 to 1.0)

        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            if languages:
                self._languages = languages
            self._confidence_threshold = confidence_threshold

            # Try Windows OCR first (fastest)
            if self._use_windows_ocr:
                try:
                    import winrt.windows.media.ocr as ocr
                    import winrt.windows.graphics.imaging as imaging
                    import winrt.windows.storage.streams as streams
                    self._ocr_method = "windows"
                    self.logger.info("Windows OCR initialized successfully")
                    self._initialized = True
                    return True
                except Exception as e:
                    self.logger.warning(f"Windows OCR not available: {e}")

            # Fallback to pytesseract (much faster than EasyOCR)
            try:
                import pytesseract
                # Test if tesseract is actually available
                pytesseract.get_tesseract_version()
                self._ocr_method = "tesseract"
                self.logger.info("Pytesseract OCR initialized successfully")
                self._initialized = True
                return True
            except Exception as e:
                self.logger.warning(f"Pytesseract not available: {e}")

            # Last resort: disable OCR but don't fail
            self._ocr_method = "disabled"
            self.logger.warning("No OCR method available - context awareness will use window titles only")
            self._initialized = True
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize any OCR method: {e}")
            return False
    
    def extract_text(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text from an image.

        Args:
            image: PIL Image object to extract text from

        Returns:
            Dictionary containing extracted text and metadata
        """
        if not self._initialized:
            self.logger.error("OCR extractor not initialized")
            return {"text": "", "words": [], "confidence": 0.0}

        try:
            # Log image properties for debugging
            self.logger.debug(f"Processing image: {image.size}, mode: {image.mode}")

            # Early exit for very large images
            total_pixels = image.size[0] * image.size[1]
            if total_pixels > 1000000:  # ~1 megapixel max
                self.logger.debug(f"Image too large ({total_pixels} pixels), skipping OCR for performance")
                return {"text": "", "words": [], "confidence": 0.0}

            # Perform OCR based on available method
            ocr_start_time = time.time()

            if self._ocr_method == "windows":
                extracted_data = self._extract_with_windows_ocr(image)
            elif self._ocr_method == "tesseract":
                extracted_data = self._extract_with_tesseract(image)
            elif self._ocr_method == "disabled":
                # OCR disabled - return empty but don't log as error
                self.logger.debug("OCR disabled, skipping text extraction")
                extracted_data = {"text": "", "words": [], "confidence": 0.0}
            else:
                # Unknown method - return empty
                extracted_data = {"text": "", "words": [], "confidence": 0.0}

            ocr_time = time.time() - ocr_start_time
            if ocr_time > 1.0:  # Log if OCR takes longer than 1 second
                self.logger.warning(f"OCR processing took {ocr_time:.2f}s")
            else:
                self.logger.debug(f"OCR completed in {ocr_time:.3f}s")

            return extracted_data

        except Exception as e:
            self.logger.error(f"Error extracting text from image: {e}")
            return {"text": "", "words": [], "confidence": 0.0}
    

    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text.
        
        Args:
            text: Raw text from OCR
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
            
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove very short "words" that are likely OCR noise
        if len(text) < 2:
            return ""
            
        # Remove text that's mostly special characters
        if len(re.sub(r'[^a-zA-Z0-9\s]', '', text)) < len(text) * 0.3:
            return ""
            
        return text
    
    def extract_keywords(self, text: str, min_length: int = 3) -> List[str]:
        """Extract potential keywords from text.
        
        Args:
            text: Text to extract keywords from
            min_length: Minimum length for keywords
            
        Returns:
            List of potential keywords
        """
        if not text:
            return []
            
        try:
            # Split into words and filter
            words = re.findall(r'\b[a-zA-Z0-9]+\b', text.lower())
            
            # Filter by length and common stop words
            stop_words = {
                'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
                'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
                'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those',
                'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
                'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
                'can', 'must', 'shall', 'a', 'an', 'as', 'if', 'when', 'where', 'why',
                'how', 'what', 'which', 'who', 'whom', 'whose', 'all', 'any', 'both',
                'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor',
                'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very'
            }
            
            keywords = []
            for word in words:
                if (len(word) >= min_length and 
                    word not in stop_words and 
                    not word.isdigit()):
                    keywords.append(word)
            
            # Remove duplicates while preserving order
            unique_keywords = []
            seen = set()
            for keyword in keywords:
                if keyword not in seen:
                    unique_keywords.append(keyword)
                    seen.add(keyword)
            
            return unique_keywords[:20]  # Limit to top 20 keywords
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords: {e}")
            return []
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """Set the confidence threshold for text detection.
        
        Args:
            threshold: New confidence threshold (0.0 to 1.0)
        """
        if 0.0 <= threshold <= 1.0:
            self._confidence_threshold = threshold
            self.logger.debug(f"OCR confidence threshold set to {threshold}")
        else:
            self.logger.warning(f"Invalid confidence threshold: {threshold}")
    
    def set_languages(self, languages: List[str]) -> bool:
        """Set the languages for OCR detection.

        Args:
            languages: List of language codes

        Returns:
            True if languages were set successfully, False otherwise
        """
        try:
            if languages != self._languages:
                self._languages = languages
                self.logger.info(f"OCR languages updated to: {languages}")
                # Note: Language switching would require reinitializing the OCR engine
                # For now, we'll just update the setting
            return True
        except Exception as e:
            self.logger.error(f"Error setting OCR languages: {e}")
            return False
    
    def is_initialized(self) -> bool:
        """Check if the OCR extractor is initialized.

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized and self._ocr_method is not None
    
    def _extract_with_windows_ocr(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text using Windows OCR API (fastest method).

        Args:
            image: PIL Image to process

        Returns:
            Extracted text data
        """
        try:
            import winrt.windows.media.ocr as ocr
            import winrt.windows.graphics.imaging as imaging
            import winrt.windows.storage.streams as streams
            import io

            # Convert PIL image to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()

            # Create Windows OCR engine
            ocr_engine = ocr.OcrEngine.try_create_from_language(ocr.Language.english)
            if not ocr_engine:
                ocr_engine = ocr.OcrEngine.try_create_from_user_profile_languages()

            # This is a simplified version - Windows OCR integration would need more work
            # For now, fall back to tesseract
            return self._extract_with_tesseract(image)

        except Exception as e:
            self.logger.debug(f"Windows OCR failed: {e}")
            return self._extract_with_tesseract(image)

    def _extract_with_tesseract(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text using pytesseract (faster than EasyOCR).

        Args:
            image: PIL Image to process

        Returns:
            Extracted text data
        """
        try:
            import pytesseract

            # Configure tesseract for speed
            config = '--psm 6 --oem 3 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,!?@#$%^&*()_+-=[]{}|;:,.<>?/~`'

            # Extract text with confidence data
            data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)

            # Process results
            words = []
            all_text = []
            total_confidence = 0.0
            valid_results = 0

            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                confidence = float(data['conf'][i]) / 100.0  # Convert to 0-1 range

                if confidence >= self._confidence_threshold and len(text) > 0:
                    cleaned_text = self._clean_text(text)
                    if cleaned_text:
                        words.append({
                            "text": cleaned_text,
                            "confidence": confidence,
                            "bbox": [data['left'][i], data['top'][i],
                                   data['left'][i] + data['width'][i],
                                   data['top'][i] + data['height'][i]]
                        })
                        all_text.append(cleaned_text)
                        total_confidence += confidence
                        valid_results += 1

            avg_confidence = total_confidence / valid_results if valid_results > 0 else 0.0
            full_text = " ".join(all_text)

            self.logger.debug(f"Tesseract extracted {len(words)} words with avg confidence {avg_confidence:.2f}")

            return {
                "text": full_text,
                "words": words,
                "confidence": avg_confidence,
                "word_count": len(words)
            }

        except Exception as e:
            self.logger.error(f"Tesseract OCR failed: {e}")
            # Return empty result
            return {"text": "", "words": [], "confidence": 0.0}

    def cleanup(self) -> None:
        """Clean up OCR resources."""
        self._ocr_method = None
        self._initialized = False
        self.logger.debug("OCR extractor resources cleaned up")
