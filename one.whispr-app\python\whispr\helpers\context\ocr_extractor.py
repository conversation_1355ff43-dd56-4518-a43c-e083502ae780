"""
OCR text extraction functionality for context awareness.

Uses EasyOCR with GPU acceleration and optimized settings for speed and accuracy.
"""

import logging
import re
import time
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import numpy as np
import easyocr


class OCRExtractor:
    """Manages OCR text extraction from images."""
    
    def __init__(self):
        """Initialize the OCR extractor."""
        self.logger = logging.getLogger("whispr.context.ocr")
        self._reader = None
        self._languages = ['en']  # Default to English
        self._confidence_threshold = 0.5
        self._initialized = False
        self._gpu_available = False
        
    def initialize(self, languages: Optional[List[str]] = None, confidence_threshold: float = 0.5) -> bool:
        """Initialize the OCR system with GPU acceleration.

        Args:
            languages: List of language codes (e.g., ['en', 'es', 'fr'])
            confidence_threshold: Minimum confidence threshold for text detection (0.0 to 1.0)

        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            if languages:
                self._languages = languages
            self._confidence_threshold = confidence_threshold

            self.logger.info(f"Initializing EasyOCR with languages: {self._languages}")

            # Try GPU first for maximum speed
            try:
                self._reader = easyocr.Reader(
                    self._languages,
                    gpu=True,
                    model_storage_directory=None,  # Use default
                    download_enabled=True,
                    detector=True,
                    recognizer=True,
                    verbose=False
                )
                self._gpu_available = True
                self.logger.info("EasyOCR initialized with GPU acceleration")
            except Exception as gpu_error:
                self.logger.warning(f"GPU initialization failed: {gpu_error}")
                # Fallback to CPU
                try:
                    self._reader = easyocr.Reader(
                        self._languages,
                        gpu=False,
                        model_storage_directory=None,
                        download_enabled=True,
                        detector=True,
                        recognizer=True,
                        verbose=False
                    )
                    self._gpu_available = False
                    self.logger.info("EasyOCR initialized with CPU (GPU not available)")
                except Exception as cpu_error:
                    self.logger.error(f"CPU initialization also failed: {cpu_error}")
                    return False

            self._initialized = True
            self.logger.info("EasyOCR initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize EasyOCR: {e}")
            return False
    
    def extract_text(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text from an image using EasyOCR.

        Args:
            image: PIL Image object to extract text from

        Returns:
            Dictionary containing extracted text and metadata
        """
        if not self._initialized or not self._reader:
            self.logger.error("OCR extractor not initialized")
            return {"text": "", "words": [], "confidence": 0.0}

        try:
            # Convert PIL Image to numpy array
            image_array = np.array(image)

            # Log image properties for debugging
            self.logger.debug(f"Processing image: {image.size}, mode: {image.mode}, GPU: {self._gpu_available}")

            # Early exit for very large images that would be slow
            total_pixels = image_array.shape[0] * image_array.shape[1]
            if total_pixels > 1200000:  # ~1.2 megapixels max for consistent performance
                self.logger.debug(f"Image too large ({total_pixels} pixels), skipping OCR")
                return {"text": "", "words": [], "confidence": 0.0}

            # Additional check for text-heavy content (lots of small text)
            if total_pixels > 800000 and self._looks_like_terminal_content(image):
                self.logger.debug("Detected terminal-like content, skipping OCR for performance")
                return {"text": "", "words": [], "confidence": 0.0}

            # Perform OCR with optimized settings and timeout protection
            ocr_start_time = time.time()

            try:
                # Optimized EasyOCR settings for consistent performance
                results = self._reader.readtext(
                    image_array,
                    # More aggressive thresholds for speed
                    width_ths=0.7,          # Higher threshold for speed
                    height_ths=0.7,         # Higher threshold for speed
                    text_threshold=0.7,     # Higher threshold for speed
                    low_text=0.4,           # Higher threshold for speed
                    link_threshold=0.4,     # Higher threshold for speed

                    # Smaller canvas for speed
                    canvas_size=1024,       # Smaller canvas for faster processing
                    mag_ratio=1.0,          # No magnification

                    # Speed optimizations
                    paragraph=False,        # Disable for speed
                    detail=1,               # Include confidence
                    batch_size=1,

                    # Faster decoder settings
                    decoder='greedy',       # Fastest decoder
                    beamWidth=3,           # Smaller beam width for speed

                    # GPU optimizations
                    workers=0 if self._gpu_available else 1,
                )

                ocr_time = time.time() - ocr_start_time

                # Log performance with warnings for slow processing
                if ocr_time > 3.0:
                    self.logger.warning(f"OCR took {ocr_time:.2f}s - consider image optimization")
                else:
                    self.logger.debug(f"EasyOCR completed in {ocr_time:.3f}s, found {len(results)} text elements")

            except Exception as ocr_error:
                ocr_time = time.time() - ocr_start_time
                self.logger.error(f"OCR processing failed after {ocr_time:.2f}s: {ocr_error}")
                return {"text": "", "words": [], "confidence": 0.0}

            # Process results with proper error handling
            extracted_data = self._process_easyocr_results(results)

            return extracted_data

        except Exception as e:
            self.logger.error(f"Error extracting text from image: {e}")
            return {"text": "", "words": [], "confidence": 0.0}
    

    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text.
        
        Args:
            text: Raw text from OCR
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
            
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove very short "words" that are likely OCR noise
        if len(text) < 2:
            return ""
            
        # Remove text that's mostly special characters
        if len(re.sub(r'[^a-zA-Z0-9\s]', '', text)) < len(text) * 0.3:
            return ""
            
        return text
    
    def _process_easyocr_results(self, results: List) -> Dict[str, Any]:
        """Process EasyOCR results into structured data.

        Args:
            results: Raw OCR results from EasyOCR

        Returns:
            Processed text data with confidence filtering
        """
        words = []
        all_text = []
        total_confidence = 0.0
        valid_results = 0
        rejected_count = 0

        self.logger.debug(f"Processing {len(results)} EasyOCR results with confidence threshold {self._confidence_threshold}")

        for result in results:
            try:
                # EasyOCR returns results as [bbox, text, confidence]
                if isinstance(result, (list, tuple)) and len(result) >= 3:
                    bbox, text, confidence = result[0], result[1], result[2]
                elif isinstance(result, (list, tuple)) and len(result) == 2:
                    # Some versions might return [text, confidence]
                    text, confidence = result[0], result[1]
                    bbox = None
                else:
                    self.logger.debug(f"Unexpected EasyOCR result format: {result}")
                    rejected_count += 1
                    continue

                # Ensure text is string and clean it
                text = str(text).strip()

                # Ensure confidence is float
                confidence = float(confidence)

                # Filter by confidence threshold and text length
                if confidence >= self._confidence_threshold and len(text) > 0:
                    # Clean up the text
                    cleaned_text = self._clean_text(text)

                    if cleaned_text:  # Only include non-empty text
                        words.append({
                            "text": cleaned_text,
                            "confidence": confidence,
                            "bbox": bbox
                        })
                        all_text.append(cleaned_text)
                        total_confidence += confidence
                        valid_results += 1
                else:
                    rejected_count += 1

            except Exception as e:
                self.logger.debug(f"Error processing EasyOCR result {result}: {e}")
                rejected_count += 1
                continue

        # Log processing results
        if rejected_count > 0:
            self.logger.debug(f"Rejected {rejected_count} low confidence/invalid elements")

        if words:
            sample_kept = [f"'{w['text']}' ({w['confidence']:.2f})" for w in words[:5]]
            self.logger.debug(f"Kept {len(words)} valid elements. Samples: {', '.join(sample_kept)}")
        else:
            self.logger.debug("No valid text elements found")

        # Calculate average confidence
        avg_confidence = total_confidence / valid_results if valid_results > 0 else 0.0

        # Join all text with spaces
        full_text = " ".join(all_text)

        return {
            "text": full_text,
            "words": words,
            "confidence": avg_confidence,
            "word_count": len(words)
        }

    def extract_keywords(self, text: str, min_length: int = 3) -> List[str]:
        """Extract potential keywords from text.
        
        Args:
            text: Text to extract keywords from
            min_length: Minimum length for keywords
            
        Returns:
            List of potential keywords
        """
        if not text:
            return []
            
        try:
            # Split into words and filter
            words = re.findall(r'\b[a-zA-Z0-9]+\b', text.lower())
            
            # Filter by length and common stop words
            stop_words = {
                'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
                'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
                'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those',
                'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
                'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
                'can', 'must', 'shall', 'a', 'an', 'as', 'if', 'when', 'where', 'why',
                'how', 'what', 'which', 'who', 'whom', 'whose', 'all', 'any', 'both',
                'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor',
                'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very'
            }
            
            keywords = []
            for word in words:
                if (len(word) >= min_length and 
                    word not in stop_words and 
                    not word.isdigit()):
                    keywords.append(word)
            
            # Remove duplicates while preserving order
            unique_keywords = []
            seen = set()
            for keyword in keywords:
                if keyword not in seen:
                    unique_keywords.append(keyword)
                    seen.add(keyword)
            
            return unique_keywords[:20]  # Limit to top 20 keywords
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords: {e}")
            return []
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """Set the confidence threshold for text detection.
        
        Args:
            threshold: New confidence threshold (0.0 to 1.0)
        """
        if 0.0 <= threshold <= 1.0:
            self._confidence_threshold = threshold
            self.logger.debug(f"OCR confidence threshold set to {threshold}")
        else:
            self.logger.warning(f"Invalid confidence threshold: {threshold}")
    
    def set_languages(self, languages: List[str]) -> bool:
        """Set the languages for OCR detection.

        Args:
            languages: List of language codes

        Returns:
            True if languages were set successfully, False otherwise
        """
        try:
            if languages != self._languages:
                self._languages = languages
                self.logger.info(f"EasyOCR languages updated to: {languages}")
                # Reinitialize reader with new languages for immediate effect
                if self._initialized:
                    self.logger.info("Reinitializing EasyOCR with new languages...")
                    gpu_mode = self._gpu_available
                    self._reader = easyocr.Reader(
                        self._languages,
                        gpu=gpu_mode,
                        verbose=False
                    )
                    self.logger.info("EasyOCR reinitialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error setting EasyOCR languages: {e}")
            return False
    
    def is_initialized(self) -> bool:
        """Check if the OCR extractor is initialized.

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized and self._reader is not None
    




    def _looks_like_terminal_content(self, image: Image.Image) -> bool:
        """Check if image looks like terminal/editor content with lots of small text.

        Args:
            image: PIL Image to analyze

        Returns:
            True if image appears to be terminal-like content
        """
        try:
            # Convert to grayscale for analysis
            gray_image = image.convert('L')
            image_array = np.array(gray_image)

            # Calculate text density by looking at pixel variation
            # Terminal content typically has high contrast and lots of small text

            # Look for high contrast patterns (text on background)
            edges = np.abs(np.diff(image_array, axis=1))
            high_contrast_pixels = np.sum(edges > 50)  # Threshold for text edges
            total_pixels = image_array.size

            contrast_ratio = high_contrast_pixels / total_pixels

            # Terminal content typically has >5% high-contrast pixels
            is_text_heavy = contrast_ratio > 0.05

            if is_text_heavy:
                self.logger.debug(f"High text density detected: {contrast_ratio:.3f}")

            return is_text_heavy

        except Exception as e:
            self.logger.debug(f"Error analyzing image content: {e}")
            return False

    def cleanup(self) -> None:
        """Clean up OCR resources."""
        self._reader = None
        self._initialized = False
        self._gpu_available = False
        self.logger.debug("EasyOCR extractor resources cleaned up")
