"""
WebSocket server implementation for One.Whispr.

This module contains the WebSocketServer class that handles communication
with the Electron frontend over WebSockets.
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, List, Any, Optional, Callable, Awaitable, Set

import websockets

from whispr.core.base import BaseService, ServiceContainer, Event


class WebSocketServer(BaseService):
    """WebSocket server for communication with the Electron frontend."""
    
    def __init__(self, service_container: Optional[ServiceContainer] = None,
                 host: str = '127.0.0.1', port: int = 0):
        """Initialize the WebSocket server.
        
        Args:
            service_container: The service container for dependency resolution
            host: The host to bind to
            port: The port to bind to (0 for auto-selection)
        """
        super().__init__(service_container)
        self.host = host
        self.port = port
        self._actual_port = None
        self._running = False
        self.server = None
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.event_listeners: Dict[str, List[Callable[[Event], Awaitable[None]]]] = {}
        self.command_handlers: Dict[str, Callable[[Dict[str, Any], Dict[str, Any]], Awaitable[Dict[str, Any]]]] = {}
        
        # Add client connect/disconnect event hooks
        self.on_client_connect = None
        self.on_client_disconnect = None
        self._main_loop = None
    
    async def initialize(self) -> bool:
        """Initialize the WebSocket server.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            config_manager = self.get_service("config")
            if config_manager:
                websocket_config = config_manager.get("websocket") or {}
                self.host = websocket_config.get("host", self.host)
                self.port = websocket_config.get("port", self.port)
                
            return await super().initialize()
        except Exception as e:
            self.logger.error(f"Error initializing WebSocketServer: {e}")
            return False
    
    async def start(self) -> Optional[int]:
        """Start the WebSocket server.
        
        Returns:
            The actual port the server is listening on, or None if startup failed
        """
        if self._running:
            return self._actual_port
        
        try:
            # Store reference to the main event loop
            self._main_loop = asyncio.get_event_loop()
            
            # Create a handler adapter function for newer websockets versions
            async def handler_adapter(websocket):
                await self._handle_client(websocket, "/")
            
            self.server = await websockets.serve(
                handler_adapter, self.host, self.port
            )
            
            # Get the actual port (might be different if port was 0)
            for sock in self.server.sockets:
                self._actual_port = sock.getsockname()[1]
                break
            
            self._running = True
            self.logger.info(f"WebSocket server started on ws://{self.host}:{self._actual_port}")
            
            # Also print to stdout for Electron to detect
            print(f"WebSocket server started on {self.host}:{self._actual_port}", flush=True)
            
            return self._actual_port
        except Exception as e:
            self.logger.error(f"Error starting WebSocket server: {e}")
            return None
    
    async def cleanup(self) -> bool:
        """Clean up resources.
        
        Returns:
            True if cleanup was successful, False otherwise
        """
        if not self._running:
            return True
        
        try:
            # Close all client connections
            close_tasks = [client.close() for client in self.clients]
            if close_tasks:
                await asyncio.gather(*close_tasks, return_exceptions=True)
            
            # Close the server
            if self.server:
                self.server.close()
                await self.server.wait_closed()
            
            self._running = False
            self.logger.info("WebSocket server stopped")
            return await super().cleanup()
        except Exception as e:
            self.logger.error(f"Error cleaning up WebSocketServer: {e}")
            return False
    
    async def _handle_client(self, websocket: websockets.WebSocketServerProtocol, path: str) -> None:
        """Handle a client connection.
        
        Args:
            websocket: The WebSocket connection
            path: The connection path
        """
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        self.logger.info(f"Client connected: {client_address}")
        
        # Store client connection
        self.clients.add(websocket)
        
        # Attach message handler to the client for custom response handling
        websocket._message_handler = None
        
        # Call the client connect hook if defined
        if self.on_client_connect:
            try:
                await self.on_client_connect(websocket)
            except Exception as e:
                self.logger.error(f"Error in client connect hook: {e}")
        
        try:
            async for message in websocket:
                try:
                    # Parse the message
                    data = json.loads(message)
                    
                    # Check if client has a custom message handler
                    if websocket._message_handler:
                        # Let the custom handler process the message first
                        await websocket._message_handler(websocket, message)
                        continue
                    
                    # Handle standard message types
                    message_type = data.get("type")
                    
                    if message_type == "ping":
                        # Handle ping messages
                        response = {
                            "type": "pong",
                            "timestamp": data.get("timestamp", 0)
                        }
                        await websocket.send(json.dumps(response))
                    
                    elif message_type == "request":
                        # Handle command requests
                        command = data.get("command")
                        params = data.get("params", {})
                        message_id = data.get("id")
                        
                        handler = self.command_handlers.get(command)
                        if handler:
                            # Create context for the handler with service container
                            context = {
                                "websocket": websocket, 
                                "client_address": client_address,
                                "service_container": self.service_container,  # Add service container to context
                                "message_id": message_id  # Include the message_id in context for direct responses
                            }
                            
                            # Call the handler
                            result = await handler(params, context)
                            
                            # Send the response
                            response = {
                                "type": "response",
                                "id": message_id,
                                "result": result
                            }
                            await websocket.send(json.dumps(response))
                        else:
                            # Unknown command
                            self.logger.warning(f"Unknown command: {command}")
                            response = {
                                "type": "response",
                                "id": message_id,
                                "error": f"Unknown command: {command}"
                            }
                            await websocket.send(json.dumps(response))
                    
                    else:
                        # Unknown message type
                        self.logger.warning(f"Unknown message type: {message_type}")
                
                except json.JSONDecodeError:
                    self.logger.warning(f"Invalid JSON received from {client_address}")
                except Exception as e:
                    self.logger.error(f"Error handling message from {client_address}: {e}")
        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"Client disconnected: {client_address}")
        finally:
            # Remove the client from the set
            if websocket in self.clients:
                self.clients.remove(websocket)
            
            # Call the client disconnect hook if defined
            if self.on_client_disconnect:
                try:
                    await self.on_client_disconnect(websocket)
                except Exception as e:
                    self.logger.error(f"Error in client disconnect hook: {e}")
    
    def register_command_handler(self, command: str, handler: Callable) -> None:
        """Register a command handler.
        
        Args:
            command: The command to handle
            handler: The handler function
        """
        self.command_handlers[command] = handler
        self.logger.debug(f"Registered command handler: {command}")

    def unregister_handler(self, command: str) -> None:
        """Unregister a command handler.
        
        Args:
            command: The command to unregister
        """
        if command in self.command_handlers:
            del self.command_handlers[command]
            self.logger.debug(f"Unregistered command handler: {command}")
    
    def register_event_listener(self, event_type: str, listener: Callable[[Event], Awaitable[None]]) -> None:
        """Register an event listener.
        
        Args:
            event_type: The event type to listen for
            listener: The listener function
        """
        if event_type not in self.event_listeners:
            self.event_listeners[event_type] = []
        
        self.event_listeners[event_type].append(listener)
        self.logger.debug(f"Registered event listener for: {event_type}")
    
    async def dispatch_event(self, event: Event) -> None:
        """Dispatch an event to registered listeners and clients.
        
        Args:
            event: The event to dispatch
        """
        event_type = event.event_type
        
        # Notify listeners
        if event_type in self.event_listeners:
            for listener in self.event_listeners[event_type]:
                try:
                    await listener(event)
                except Exception as e:
                    self.logger.error(f"Error in event listener for {event_type}: {e}")
        
        # Send to all clients
        if self.clients:
            message = json.dumps(event.to_dict())
            send_tasks = [client.send(message) for client in self.clients]
            if send_tasks:
                await asyncio.gather(*send_tasks, return_exceptions=True)
    
    async def send_command(self, client: websockets.WebSocketServerProtocol, 
                          command: str, params: Dict[str, Any] = None) -> Any:
        """Send a command to a client and wait for a response.
        
        Args:
            client: The WebSocket client
            command: The command to send
            params: The command parameters
            
        Returns:
            The command result
        """
        if not params:
            params = {}
            
        # Generate a unique ID for this command
        command_id = str(uuid.uuid4())
        
        # Create the message
        message = {
            "type": "request",
            "id": command_id,
            "command": command,
            "params": params
        }
        
        # Create a future to wait for the response
        future = asyncio.get_event_loop().create_future()
        
        # Define a handler for the response
        async def response_handler(websocket, data):
            try:
                response = json.loads(data)
                if response.get("id") == command_id:
                    if "error" in response:
                        future.set_exception(Exception(response["error"]))
                    else:
                        future.set_result(response.get("result"))
                    return True
            except Exception as e:
                self.logger.error(f"Error handling response: {e}")
            return False
            
        # Set up temporary message handler
        original_message_handler = client._message_handler
        client._message_handler = response_handler
        
        try:
            # Send the command
            await client.send(json.dumps(message))
            
            # Wait for the response with a timeout
            try:
                return await asyncio.wait_for(future, timeout=30.0)
            except asyncio.TimeoutError:
                raise Exception(f"Command {command} timed out")
        finally:
            # Restore the original message handler
            client._message_handler = original_message_handler
    
    @property
    def actual_port(self) -> Optional[int]:
        """Get the actual port the server is listening on.
        
        Returns:
            The port number or None if the server is not running
        """
        return self._actual_port
    
    async def broadcast(self, message: str) -> bool:
        """Broadcast a message to all connected clients.
        
        Args:
            message: The message to broadcast
            
        Returns:
            True if the message was sent to at least one client, False otherwise
        """
        if not self.clients:
            return False
            
        try:
            # Send message to all clients
            send_tasks = [client.send(message) for client in self.clients.copy()]
            if send_tasks:
                results = await asyncio.gather(*send_tasks, return_exceptions=True)
                # Return True if at least one send was successful
                return any(not isinstance(result, Exception) for result in results)
            return False
        except Exception as e:
            self.logger.error(f"Error broadcasting message: {e}")
            return False
    
    def broadcast_sync(self, message: str) -> bool:
        """Broadcast a message to all connected clients synchronously.
        
        Args:
            message: The message to broadcast
            
        Returns:
            True if the message was sent to at least one client, False otherwise
        """
        if not self.clients:
            # Always return True when there are no clients - this is important for initial sync
            # to avoid causing timeouts in the main process
            return True
            
        try:
            # Use the stored main loop reference for simpler approach
            loop = self._main_loop
            if loop and not loop.is_closed():
                # Schedule the broadcast in the main loop
                future = asyncio.run_coroutine_threadsafe(self.broadcast(message), loop)
                return future.result(timeout=5.0)  # 5 second timeout
            else:
                # If no main loop is available, we can't send messages
                # This is normal now with the removal of event_broadcaster
                return True  # Return success even if we can't actually send
        except asyncio.TimeoutError:
            # Timeouts are common and not critical errors
            return False
        except Exception as e:
            # Silent fail for expected errors after event broadcaster removal
            # Only log errors that aren't related to normal event broadcasting
            if "event" not in str(e).lower() and "broadcast" not in str(e).lower():
                self.logger.error(f"Error broadcasting message synchronously: {e}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """Get the current service status.
        
        Returns:
            A dictionary containing status information
        """
        status = super().get_status()
        status.update({
            "host": self.host,
            "port": self._actual_port or self.port,
            "running": self._running,
            "clients": len(self.clients),
            "command_handlers": list(self.command_handlers.keys()),
            "event_listeners": {et: len(listeners) for et, listeners in self.event_listeners.items()}
        })
        return status 