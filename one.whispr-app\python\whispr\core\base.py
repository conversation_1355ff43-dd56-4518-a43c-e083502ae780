"""
Base classes and interfaces for the One.Whispr application.

This module contains core abstractions and base classes used throughout the application.
"""

import logging
from typing import Dict, Any, Optional, List, TypeVar, Type


class ServiceContainer:
    """Container for service registration and resolution."""
    
    def __init__(self):
        """Initialize the service container."""
        self._services = {}
        self.logger = logging.getLogger("whispr.ServiceContainer")
    
    def register(self, service_type: str, instance: Any) -> None:
        """Register a service.
        
        Args:
            service_type: The type or name of the service
            instance: The service instance
        """
        self._services[service_type] = instance
        self.logger.debug(f"Registered service: {service_type}")
    
    def resolve(self, service_type: str) -> Any:
        """Resolve a service.
        
        Args:
            service_type: The type or name of the service
            
        Returns:
            The service instance or None if not found
        """
        if service_type not in self._services:
            self.logger.warning(f"Service not found: {service_type}")
            return None
            
        return self._services.get(service_type)
    
    def get_service(self, service_type: str) -> Any:
        """Get a service (alias for resolve).
        
        Args:
            service_type: The type or name of the service
            
        Returns:
            The service instance or None if not found
        """
        return self.resolve(service_type)
    
    def has_service(self, service_type: str) -> bool:
        """Check if a service is registered.
        
        Args:
            service_type: The type or name of the service
            
        Returns:
            True if the service is registered, False otherwise
        """
        return service_type in self._services
    
    def get_services(self) -> List[str]:
        """Get a list of registered service types.
        
        Returns:
            A list of registered service types
        """
        return list(self._services.keys())


class BaseService:
    """Base class for all services in the application."""
    
    def __init__(self, service_container: Optional[ServiceContainer] = None):
        """Initialize the service.
        
        Args:
            service_container: The service container for dependency resolution
        """
        self.service_container = service_container
        self.logger = logging.getLogger(f"whispr.{self.__class__.__name__}")
        self._is_initialized = False
    
    async def initialize(self) -> bool:
        """Initialize the service. To be implemented by subclasses.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        self._is_initialized = True
        return True
    
    async def cleanup(self) -> bool:
        """Clean up resources. To be implemented by subclasses.
        
        Returns:
            True if cleanup was successful, False otherwise
        """
        self._is_initialized = False
        return True
    
    def update_config(self, config: Dict[str, Any]) -> bool:
        """Update the service configuration.
        
        Args:
            config: The new configuration
            
        Returns:
            True if the update was successful, False otherwise
        """
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current service status.
        
        Returns:
            A dictionary containing status information
        """
        return {
            "initialized": self._is_initialized,
            "service_type": self.__class__.__name__
        }
    
    def get_service(self, service_type: str) -> Any:
        """Get a service from the service container.
        
        Args:
            service_type: The type or name of the service
            
        Returns:
            The service instance or None if not found
        """
        if not self.service_container:
            self.logger.warning(f"No service container available, can't resolve {service_type}")
            return None
            
        return self.service_container.resolve(service_type)


class CommandHandler:
    """Interface for command handlers."""
    
    def __init__(self, service_container: Optional[ServiceContainer] = None):
        """Initialize the command handler.
        
        Args:
            service_container: The service container for dependency resolution
        """
        self.service_container = service_container
        self.logger = logging.getLogger(f"whispr.command.{self.__class__.__name__}")
    
    def get_command_type(self) -> str:
        """Get the command type this handler can process.
        
        Returns:
            The command type as a string
        """
        raise NotImplementedError("Subclasses must implement get_command_type")
    
    async def handle(self, command: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle a command.
        
        Args:
            command: The command to handle
            context: The command execution context
            
        Returns:
            The command result
        """
        raise NotImplementedError("Subclasses must implement handle")
    
    def get_service(self, service_type: str) -> Any:
        """Get a service from the service container.
        
        Args:
            service_type: The type or name of the service
            
        Returns:
            The service instance or None if not found
        """
        if not self.service_container:
            self.logger.warning(f"No service container available, can't resolve {service_type}")
            return None
            
        return self.service_container.resolve(service_type)


class Event:
    """Base event class for the event system."""
    
    def __init__(self, event_type: str, data: Optional[Dict[str, Any]] = None):
        """Initialize an event.
        
        Args:
            event_type: The type of the event
            data: The event data
        """
        self.event_type = event_type
        self.data = data or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the event to a dictionary.
        
        Returns:
            A dictionary representation of the event
        """
        return {
            "type": "event",
            "event": self.event_type,
            "data": self.data
        }


class EventListener:
    """Interface for event listeners."""
    
    async def on_event(self, event: Event) -> None:
        """Handle an event.
        
        Args:
            event: The event to handle
        """
        raise NotImplementedError("Subclasses must implement on_event")
    
    def get_event_types(self) -> List[str]:
        """Get the event types this listener is interested in.
        
        Returns:
            A list of event types
        """
        raise NotImplementedError("Subclasses must implement get_event_types") 