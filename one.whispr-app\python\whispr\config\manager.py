"""
Configuration manager for the One.Whispr application.

This module provides functionality for loading and accessing application 
configuration in a push-based architecture. Configuration updates come from 
Electron via DatabaseManager - Python services only read, never update directly.
"""

import json
import logging
import os
import threading
import time
from typing import Dict, Any, Optional

from whispr.config.settings import DEFAULT_CONFIG


class ConfigurationManager:
    """Configuration manager for pure push-based architecture.
    
    Provides read-only access to configuration data for Python services.
    Only DatabaseManager should call internal update methods when receiving 
    database changes from Electron. Services should never update config directly.
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize the configuration manager.
        
        Args:
            config_file: Path to the configuration file
        """
        self.logger = logging.getLogger("whispr.config")
        self.config = {}
        self.config_file = config_file

        self._lock = threading.RLock()  # Thread-safe access
        self._last_modification_time = {}  # Track modification times
        
        # Initialize with default configuration
        self._load_defaults()
    
    def _load_defaults(self) -> None:
        """Load default configuration values."""
        with self._lock:
            self.config = DEFAULT_CONFIG.copy()
            self._last_modification_time = {section: time.time() for section in self.config.keys()}
            self.logger.debug("Loaded default configuration")
    
    def load(self) -> bool:
        """Load configuration from file.
        
        Returns:
            True if configuration was loaded successfully, False otherwise
        """
        if not self.config_file:
            self.logger.debug("No configuration file specified, using defaults")
            return True
            
        if not os.path.exists(self.config_file):
            self.logger.warning(f"Configuration file not found: {self.config_file}")
            # Try to create with defaults
            return self.save()
            
        try:
            with open(self.config_file, 'r') as f:
                file_config = json.load(f)
            
            # Merge with current configuration
            with self._lock:
                self._merge_config(file_config)
                self._update_modification_times()
                
            self.logger.info(f"Loaded configuration from {self.config_file}")
            

            
            return True
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in configuration file: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            return False
    
    def save(self, config_file: Optional[str] = None) -> bool:
        """Save configuration to file.
        
        Args:
            config_file: Path to the configuration file, defaults to self.config_file
            
        Returns:
            True if configuration was saved successfully, False otherwise
        """
        target_file = config_file or self.config_file
        
        if not target_file:
            self.logger.warning("No configuration file specified, can't save configuration")
            return False
            
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(os.path.abspath(target_file)), exist_ok=True)
            
            # Save configuration
            with self._lock:
                with open(target_file, 'w') as f:
                    json.dump(self.config, f, indent=2)
                    
            self.logger.info(f"Saved configuration to {target_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            return False
    
    def _merge_config(self, new_config: Dict[str, Any], target: Optional[Dict[str, Any]] = None, 
                     path: Optional[list] = None) -> Dict[str, Any]:
        """Recursively merge configuration dictionaries.
        
        Args:
            new_config: The new configuration dictionary
            target: The target dictionary to merge into
            path: The current path in the configuration hierarchy
            
        Returns:
            The merged configuration dictionary
        """
        if target is None:
            target = self.config
        if path is None:
            path = []
            
        for key, value in new_config.items():
            new_path = path + [key]
            
            if isinstance(value, dict) and key in target and isinstance(target[key], dict):
                # Recursively merge nested dictionaries
                self._merge_config(value, target[key], new_path)
            else:
                # Replace or add the value
                target[key] = value
                self.logger.debug(f"Set configuration value: {'.'.join(new_path)} = {value}")
                
        return target
    
    def get(self, section: str, key: Optional[str] = None, default: Any = None) -> Any:
        """Get a configuration value with thread safety.
        
        Args:
            section: The configuration section
            key: The specific key (or None for the entire section)
            default: Default value if the key is not found
            
        Returns:
            The configuration value or default
        """
        with self._lock:
            if section not in self.config:
                self.logger.debug(f"Configuration section not found: {section}, returning default")
                return default
                
            if key is None:
                return self.config[section].copy()  # Return a copy for thread safety
                
            return self.config[section].get(key, default)
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get the entire configuration with thread safety.
        
        Returns:
            The complete configuration dictionary (copy)
        """
        with self._lock:
            return self.config.copy()
    
    # Internal methods for DatabaseManager to push configuration updates
    # These should ONLY be called by DatabaseManager, never by Python services
    
    def _update_from_database(self, section: str, key: str, value: Any) -> bool:
        """Internal method for DatabaseManager to push configuration updates.
        
        This method should ONLY be called by DatabaseManager when receiving 
        database changes from Electron. Python services should never call this directly.
        
        Args:
            section: The configuration section
            key: The specific key
            value: The new value
            
        Returns:
            True if the update was successful, False otherwise
        """
        try:
            with self._lock:
                if section not in self.config:
                    self.config[section] = {}
                
                # Store old value for comparison
                old_value = self.config[section].get(key)
                
                # Update the value
                self.config[section][key] = value
                
                # Update modification time
                self._last_modification_time[section] = time.time()
                

                
            self.logger.debug(f"Database pushed configuration update: {section}.{key} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error processing database configuration update {section}.{key}: {e}")
            return False
    

    
    def _update_modification_times(self) -> None:
        """Update modification times for all sections."""
        current_time = time.time()
        for section in self.config.keys():
            self._last_modification_time[section] = current_time
    
    def get_section_modification_time(self, section: str) -> Optional[float]:
        """Get the last modification time for a configuration section.
        
        Args:
            section: The configuration section
            
        Returns:
            Last modification timestamp or None if section doesn't exist
        """
        return self._last_modification_time.get(section)
    
    def get_all(self) -> Dict[str, Any]:
        """Get the entire configuration (legacy method for compatibility).
        
        Returns:
            The complete configuration dictionary
        """
        return self.get_all_config()
    
    # Convenience methods for database table access (merged from ConfigurationService)
    
    def get_settings(self) -> Dict[str, Any]:
        """Get all settings from the settings table.
        
        Returns:
            Settings dict with keys like selectedInputDevice, recordingMode, globalSystemPrompt, etc.
        """
        try:
            settings = self.get("settings") or {}
            return settings.get("data", {})
        except Exception as e:
            self.logger.error(f"Error getting settings: {e}")
            return {}

    def get_all_modes(self) -> Dict[str, Dict[str, Any]]:
        """Get all modes from the modes table.
        
        Returns:
            Dict mapping mode_id to mode data (name, isActive, configuration, etc.)
        """
        try:
            modes = self.get("modes") or {}
            return modes.get("data", {})
        except Exception as e:
            self.logger.error(f"Error getting all modes: {e}")
            return {}

    def get_active_mode(self) -> Dict[str, Any]:
        """Get the currently active mode.
        
        Returns:
            Active mode dict with id added, or empty dict if none found
        """
        try:
            modes = self.get_all_modes()
            
            for mode_id, mode in modes.items():
                if mode.get("isActive", False):
                    return {"id": mode_id, **mode}
            
            self.logger.warning("No active mode found")
            return {}
        except Exception as e:
            self.logger.error(f"Error getting active mode: {e}")
            return {}

    def get_all_text_replacements(self) -> Dict[str, Dict[str, Any]]:
        """Get all text replacements from the text_replacements table.
        
        Returns:
            Dict mapping replacement_id to replacement data (original, replacement, modes, etc.)
        """
        try:
            replacements = self.get("text_replacements") or {}
            return replacements.get("data", {})
        except Exception as e:
            self.logger.error(f"Error getting text replacements: {e}")
            return {}

    def get_text_replacements_by_active_mode(self) -> Dict[str, Dict[str, Any]]:
        """Get text replacements that apply to the currently active mode.
        
        Returns:
            Dict of replacements that either have empty modes array (apply to all) 
            or include the active mode in their modes array
        """
        try:
            active_mode = self.get_active_mode()
            if not active_mode:
                return {}
            
            active_mode_id = active_mode.get("id")
            all_replacements = self.get_all_text_replacements()
            
            applicable_replacements = {}
            for repl_id, repl_data in all_replacements.items():
                if not repl_data.get("isActive", True):
                    continue
                    
                modes = repl_data.get("modes", [])
                # Apply if modes is empty (applies to all) or contains active mode
                if not modes or active_mode_id in modes:
                    applicable_replacements[repl_id] = repl_data
            
            return applicable_replacements
        except Exception as e:
            self.logger.error(f"Error getting text replacements by active mode: {e}")
            return {}

    def get_all_vocabulary(self) -> Dict[str, Dict[str, Any]]:
        """Get all vocabulary items from the vocabulary table.
        
        Returns:
            Dict mapping vocab_id to vocabulary data (word, pronunciation, modes, etc.)
        """
        try:
            vocabulary = self.get("vocabulary") or {}
            return vocabulary.get("data", {})
        except Exception as e:
            self.logger.error(f"Error getting vocabulary: {e}")
            return {}

    def get_vocabulary_by_active_mode(self) -> Dict[str, Dict[str, Any]]:
        """Get vocabulary items that apply to the currently active mode.
        
        Returns:
            Dict of vocabulary items that either have empty modes array (apply to all)
            or include the active mode in their modes array
        """
        try:
            active_mode = self.get_active_mode()
            if not active_mode:
                return {}
            
            active_mode_id = active_mode.get("id")
            all_vocabulary = self.get_all_vocabulary()
            
            applicable_vocabulary = {}
            for vocab_id, vocab_data in all_vocabulary.items():
                if not vocab_data.get("isActive", True):
                    continue
                    
                modes = vocab_data.get("modes", [])
                # Apply if modes is empty (applies to all) or contains active mode
                if not modes or active_mode_id in modes:
                    applicable_vocabulary[vocab_id] = vocab_data
            
            return applicable_vocabulary
        except Exception as e:
            self.logger.error(f"Error getting vocabulary by active mode: {e}")
            return {}


class ConfigurationMixin:
    """Mixin that provides configuration access to any service.
    
    Add this to services that need clean configuration access without boilerplate.
    Services inherit from this mixin to get easy access to configuration methods
    without needing to manage the configuration manager directly.
    """
    
    def _get_config_manager(self):
        """Get the configuration manager from service container."""
        if hasattr(self, 'service_container') and self.service_container:
            return self.service_container.resolve("config")
        return None
    
    def get_settings(self) -> Dict[str, Any]:
        config_manager = self._get_config_manager()
        return config_manager.get_settings() if config_manager else {}
    
    def get_active_mode(self) -> Dict[str, Any]:
        config_manager = self._get_config_manager()
        return config_manager.get_active_mode() if config_manager else {}
    
    def get_all_modes(self) -> Dict[str, Dict[str, Any]]:
        config_manager = self._get_config_manager()
        return config_manager.get_all_modes() if config_manager else {}
    
    def get_all_text_replacements(self) -> Dict[str, Dict[str, Any]]:
        config_manager = self._get_config_manager()
        return config_manager.get_all_text_replacements() if config_manager else {}
    
    def get_text_replacements_by_active_mode(self) -> Dict[str, Dict[str, Any]]:
        config_manager = self._get_config_manager()
        return config_manager.get_text_replacements_by_active_mode() if config_manager else {}
    
    def get_all_vocabulary(self) -> Dict[str, Dict[str, Any]]:
        config_manager = self._get_config_manager()
        return config_manager.get_all_vocabulary() if config_manager else {}
    
    def get_vocabulary_by_active_mode(self) -> Dict[str, Dict[str, Any]]:
        config_manager = self._get_config_manager()
        return config_manager.get_vocabulary_by_active_mode() if config_manager else {} 