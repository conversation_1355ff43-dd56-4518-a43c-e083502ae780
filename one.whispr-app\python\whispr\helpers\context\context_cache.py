"""
Context caching system for managing context data with TTL, deduplication,
and efficient retrieval mechanisms.
"""

import logging
import time
import hashlib
import json
from typing import Dict, List, Optional, Any, Tuple
from collections import OrderedDict


class ContextCache:
    """Manages caching of context data with TTL and deduplication."""
    
    def __init__(self, max_size: int = 100, default_ttl: int = 3600):
        """Initialize the context cache.
        
        Args:
            max_size: Maximum number of context entries to keep
            default_ttl: Default time-to-live in seconds
        """
        self.logger = logging.getLogger("whispr.context.cache")
        self._cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self._max_size = max_size
        self._default_ttl = default_ttl
        self._access_times: Dict[str, float] = {}
        
    def store_context(self, context_data: Dict[str, Any], ttl: Optional[int] = None) -> str:
        """Store context data in the cache.
        
        Args:
            context_data: Context data to store
            ttl: Time-to-live in seconds (uses default if None)
            
        Returns:
            Cache key for the stored context
        """
        try:
            # Generate cache key based on content
            cache_key = self._generate_cache_key(context_data)
            
            # Check if we already have this context (deduplication)
            if cache_key in self._cache:
                # Update access time and move to end
                self._access_times[cache_key] = time.time()
                self._cache.move_to_end(cache_key)
                self.logger.debug(f"Context already cached: {cache_key}")
                return cache_key
            
            # Prepare cache entry
            current_time = time.time()
            ttl_seconds = ttl if ttl is not None else self._default_ttl
            
            cache_entry = {
                "data": context_data,
                "timestamp": current_time,
                "ttl": ttl_seconds,
                "expires_at": current_time + ttl_seconds,
                "access_count": 1
            }
            
            # Store in cache
            self._cache[cache_key] = cache_entry
            self._access_times[cache_key] = current_time
            
            # Enforce size limit
            self._enforce_size_limit()
            
            # Clean expired entries
            self._cleanup_expired()
            
            self.logger.debug(f"Stored context in cache: {cache_key}")
            return cache_key
            
        except Exception as e:
            self.logger.error(f"Error storing context in cache: {e}")
            return ""
    
    def get_context(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Retrieve context data from cache.
        
        Args:
            cache_key: Cache key to retrieve
            
        Returns:
            Context data or None if not found/expired
        """
        try:
            if cache_key not in self._cache:
                return None
            
            entry = self._cache[cache_key]
            current_time = time.time()
            
            # Check if expired
            if current_time > entry["expires_at"]:
                self._remove_entry(cache_key)
                return None
            
            # Update access information
            entry["access_count"] += 1
            self._access_times[cache_key] = current_time
            
            # Move to end (most recently used)
            self._cache.move_to_end(cache_key)
            
            return entry["data"]
            
        except Exception as e:
            self.logger.error(f"Error retrieving context from cache: {e}")
            return None
    
    def get_recent_contexts(self, limit: int = 10, min_relevance: float = 0.0) -> List[Dict[str, Any]]:
        """Get recent context data sorted by timestamp.
        
        Args:
            limit: Maximum number of contexts to return
            min_relevance: Minimum relevance score filter
            
        Returns:
            List of recent context data
        """
        try:
            current_time = time.time()
            recent_contexts = []
            
            # Get all valid entries sorted by timestamp (newest first)
            valid_entries = []
            for key, entry in self._cache.items():
                if current_time <= entry["expires_at"]:
                    context_data = entry["data"]
                    relevance = context_data.get("relevance_score", 0.0)
                    
                    if relevance >= min_relevance:
                        valid_entries.append((entry["timestamp"], context_data))
            
            # Sort by timestamp (newest first) and limit
            valid_entries.sort(key=lambda x: x[0], reverse=True)
            recent_contexts = [context for _, context in valid_entries[:limit]]
            
            return recent_contexts
            
        except Exception as e:
            self.logger.error(f"Error getting recent contexts: {e}")
            return []
    
    def search_contexts(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search for contexts containing specific text.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching context data
        """
        try:
            query_lower = query.lower()
            matches = []
            current_time = time.time()
            
            for key, entry in self._cache.items():
                # Skip expired entries
                if current_time > entry["expires_at"]:
                    continue
                
                context_data = entry["data"]
                
                # Search in raw text
                raw_text = context_data.get("raw_text", "").lower()
                if query_lower in raw_text:
                    matches.append((context_data, raw_text.count(query_lower)))
                    continue
                
                # Search in keywords
                keywords = context_data.get("keywords", [])
                keyword_matches = sum(1 for kw in keywords if query_lower in kw.get("keyword", "").lower())
                if keyword_matches > 0:
                    matches.append((context_data, keyword_matches))
                    continue
                
                # Search in technical terms
                tech_terms = context_data.get("technical_terms", [])
                tech_matches = sum(1 for term in tech_terms if query_lower in term.get("term", "").lower())
                if tech_matches > 0:
                    matches.append((context_data, tech_matches))
            
            # Sort by match count and limit
            matches.sort(key=lambda x: x[1], reverse=True)
            return [context for context, _ in matches[:limit]]
            
        except Exception as e:
            self.logger.error(f"Error searching contexts: {e}")
            return []
    
    def get_contexts_by_application(self, app_name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get contexts related to a specific application.
        
        Args:
            app_name: Application name to filter by
            limit: Maximum number of results
            
        Returns:
            List of context data for the application
        """
        try:
            app_name_lower = app_name.lower()
            matches = []
            current_time = time.time()
            
            for key, entry in self._cache.items():
                # Skip expired entries
                if current_time > entry["expires_at"]:
                    continue
                
                context_data = entry["data"]
                applications = context_data.get("applications", [])
                
                # Check if any detected application matches
                for app in applications:
                    if app_name_lower in app.get("name", "").lower():
                        matches.append((context_data, app.get("confidence", 0.0)))
                        break
            
            # Sort by confidence and limit
            matches.sort(key=lambda x: x[1], reverse=True)
            return [context for context, _ in matches[:limit]]
            
        except Exception as e:
            self.logger.error(f"Error getting contexts by application: {e}")
            return []
    
    def cleanup_expired(self) -> int:
        """Remove expired entries from cache.
        
        Returns:
            Number of entries removed
        """
        return self._cleanup_expired()
    
    def clear_cache(self) -> None:
        """Clear all cache entries."""
        self._cache.clear()
        self._access_times.clear()
        self.logger.info("Context cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        current_time = time.time()
        expired_count = 0
        total_access_count = 0
        
        for entry in self._cache.values():
            if current_time > entry["expires_at"]:
                expired_count += 1
            total_access_count += entry.get("access_count", 0)
        
        return {
            "total_entries": len(self._cache),
            "expired_entries": expired_count,
            "active_entries": len(self._cache) - expired_count,
            "max_size": self._max_size,
            "total_accesses": total_access_count,
            "cache_hit_ratio": self._calculate_hit_ratio()
        }
    
    def _generate_cache_key(self, context_data: Dict[str, Any]) -> str:
        """Generate a unique cache key for context data.
        
        Args:
            context_data: Context data to generate key for
            
        Returns:
            Unique cache key
        """
        # Create a hash based on the raw text and timestamp (rounded to minute)
        raw_text = context_data.get("raw_text", "")
        timestamp = context_data.get("timestamp", time.time())
        
        # Round timestamp to minute for deduplication of similar captures
        rounded_timestamp = int(timestamp // 60) * 60
        
        # Include window context for better deduplication
        window_context = context_data.get("window_context", {})
        window_title = window_context.get("title", "")
        
        key_data = f"{raw_text}|{rounded_timestamp}|{window_title}"
        return hashlib.md5(key_data.encode('utf-8')).hexdigest()
    
    def _enforce_size_limit(self) -> None:
        """Enforce maximum cache size by removing oldest entries."""
        while len(self._cache) > self._max_size:
            # Remove the oldest entry (first in OrderedDict)
            oldest_key = next(iter(self._cache))
            self._remove_entry(oldest_key)
            self.logger.debug(f"Removed oldest cache entry: {oldest_key}")
    
    def _cleanup_expired(self) -> int:
        """Remove expired entries from cache.
        
        Returns:
            Number of entries removed
        """
        current_time = time.time()
        expired_keys = []
        
        for key, entry in self._cache.items():
            if current_time > entry["expires_at"]:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_entry(key)
        
        if expired_keys:
            self.logger.debug(f"Removed {len(expired_keys)} expired cache entries")
        
        return len(expired_keys)
    
    def _remove_entry(self, key: str) -> None:
        """Remove a cache entry and its access time.
        
        Args:
            key: Cache key to remove
        """
        if key in self._cache:
            del self._cache[key]
        if key in self._access_times:
            del self._access_times[key]
    
    def _calculate_hit_ratio(self) -> float:
        """Calculate cache hit ratio.
        
        Returns:
            Hit ratio as a float between 0.0 and 1.0
        """
        # This is a simplified calculation
        # In a real implementation, you'd track hits and misses
        if not self._cache:
            return 0.0
        
        total_accesses = sum(entry.get("access_count", 0) for entry in self._cache.values())
        if total_accesses == 0:
            return 0.0
        
        # Estimate hit ratio based on access patterns
        return min(1.0, total_accesses / (len(self._cache) * 2))
