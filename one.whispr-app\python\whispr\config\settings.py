"""
Default configuration settings for the One.Whispr application.

This module contains default configuration values and definitions.
"""

from typing import Dict, Any

# Application settings
APP_SETTINGS: Dict[str, Any] = {
    'name': 'One.Whispr',
    'version': '1.0.0',
    'description': 'Audio transcription and shortcut application'
}

# WebSocket server settings
WEBSOCKET_SETTINGS: Dict[str, Any] = {
    'host': '127.0.0.1',
    'port': 0,  # 0 = Auto-select port
    'heartbeat_interval': 30,  # Seconds
    'connection_timeout': 60  # Seconds
}

# Audio settings (core technical settings only - user preferences come from database)
# Note: Audio technical settings are hardcoded in audio components:
# - AudioCapture: 16kHz sample rate, 100ms chunks
# - RealtimeLevelMonitor: 44.1kHz sample rate, 50ms update interval
# - Device testing: 5 second duration
# User preferences like device selection come from database via ConfigurationManager
# VAD is an optional utility that consumers can use
AUDIO_SETTINGS: Dict[str, Any] = {
    # Audio processing settings
    'enable_vad_filtering': True,
    'mic_vad_threshold': 0.5,      # Base threshold for microphone VAD
    'system_vad_threshold': 0.4,   # Base threshold for system audio VAD (slightly more sensitive)
    'apply_vad_to_recording': False,  # Usually False - always record everything
}

# Transcription settings (core technical settings only - user preferences come from database)
TRANSCRIPTION_SETTINGS: Dict[str, Any] = {
    # Model configuration
    'default_model': 'openai/whisper-base',
}

# Context awareness settings (core technical settings only - user preferences come from database)
CONTEXT_SETTINGS: Dict[str, Any] = {
    # Default technical settings for context awareness
    'default_ocr_languages': ['en'],
    'default_ocr_confidence_threshold': 0.1,  # Very low threshold to capture more text
    'default_cache_ttl': 3600,  # 1 hour
    'default_capture_interval': 15,  # seconds between fallback captures
    'window_check_interval': 2,  # seconds between active window checks
    'capture_cooldown_seconds': 2,  # Minimum time between captures (reduced for responsiveness)
}

# Database settings
DATABASE_SETTINGS: Dict[str, Any] = {
    'sync_on_startup': True
}

# Logging settings
LOGGING_SETTINGS: Dict[str, Any] = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': None
}

# Event broadcasting settings
EVENT_BROADCAST_SETTINGS: Dict[str, Any] = {
    'batch_size': 10,
    'batch_timeout_ms': 100,
    'max_queue_size': 10000,
    'enable_event_history': True,
    'debug_mode': False
}

# Default configuration
DEFAULT_CONFIG: Dict[str, Dict[str, Any]] = {
    'app': APP_SETTINGS,
    'websocket': WEBSOCKET_SETTINGS,
    'audio': AUDIO_SETTINGS,
    'transcription': TRANSCRIPTION_SETTINGS,
    'context': CONTEXT_SETTINGS,
    'database': DATABASE_SETTINGS,
    'logging': LOGGING_SETTINGS,
    'event_broadcast': EVENT_BROADCAST_SETTINGS,
}