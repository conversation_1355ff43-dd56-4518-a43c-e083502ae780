"""
Context Awareness Handlers

Simple command routing for context awareness operations without business logic.
Clean response formatting and error handling.
"""

import logging
from typing import Dict, Any

from whispr.core.response_utils import success_response, error_response, ErrorCodes

logger = logging.getLogger(__name__)


# Context Capture Handlers
async def handle_capture_context(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle context capture request."""
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response("Context service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Get parameters
        force = params.get("force", False)
        
        # Capture context
        context_data = await context_service.capture_context(force=force)
        
        if context_data:
            return success_response({
                "context": context_data
            }, "Context captured successfully")
        else:
            return error_response("Failed to capture context or context disabled", ErrorCodes.CAPTURE_ERROR)
            
    except Exception as e:
        logger.error(f"Error handling capture context request: {e}")
        return error_response(f"Error capturing context: {str(e)}", ErrorCodes.SERVICE_ERROR)


async def handle_get_recent_context(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle request for recent context data."""
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response("Context service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Get parameters
        limit = params.get("limit", 5)
        min_relevance = params.get("minRelevance", 0.0)
        
        # Validate parameters
        if not isinstance(limit, int) or limit < 1 or limit > 50:
            return error_response("Invalid limit parameter (must be 1-50)", ErrorCodes.PARAMETER_ERROR)
        
        if not isinstance(min_relevance, (int, float)) or min_relevance < 0.0 or min_relevance > 1.0:
            return error_response("Invalid minRelevance parameter (must be 0.0-1.0)", ErrorCodes.PARAMETER_ERROR)
        
        # Get recent contexts
        contexts = await context_service.get_recent_context(limit, min_relevance)
        
        return success_response({
            "contexts": contexts,
            "count": len(contexts)
        }, f"Retrieved {len(contexts)} recent contexts")
        
    except Exception as e:
        logger.error(f"Error handling get recent context request: {e}")
        return error_response(f"Error retrieving recent contexts: {str(e)}", ErrorCodes.SERVICE_ERROR)


async def handle_search_context(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle context search request."""
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response("Context service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Get parameters
        query = params.get("query", "").strip()
        limit = params.get("limit", 5)
        
        # Validate parameters
        if not query:
            return error_response("Query parameter is required", ErrorCodes.PARAMETER_ERROR)
        
        if not isinstance(limit, int) or limit < 1 or limit > 20:
            return error_response("Invalid limit parameter (must be 1-20)", ErrorCodes.PARAMETER_ERROR)
        
        # Search contexts
        contexts = await context_service.search_context(query, limit)
        
        return success_response({
            "contexts": contexts,
            "query": query,
            "count": len(contexts)
        }, f"Found {len(contexts)} contexts matching '{query}'")
        
    except Exception as e:
        logger.error(f"Error handling search context request: {e}")
        return error_response(f"Error searching contexts: {str(e)}", ErrorCodes.SERVICE_ERROR)


async def handle_get_context_by_application(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle request for contexts by application."""
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response("Context service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Get parameters
        app_name = params.get("appName", "").strip()
        limit = params.get("limit", 5)
        
        # Validate parameters
        if not app_name:
            return error_response("appName parameter is required", ErrorCodes.PARAMETER_ERROR)
        
        if not isinstance(limit, int) or limit < 1 or limit > 20:
            return error_response("Invalid limit parameter (must be 1-20)", ErrorCodes.PARAMETER_ERROR)
        
        # Get contexts for application
        contexts = await context_service.get_context_for_application(app_name, limit)
        
        return success_response({
            "contexts": contexts,
            "appName": app_name,
            "count": len(contexts)
        }, f"Found {len(contexts)} contexts for application '{app_name}'")
        
    except Exception as e:
        logger.error(f"Error handling get context by application request: {e}")
        return error_response(f"Error retrieving application contexts: {str(e)}", ErrorCodes.SERVICE_ERROR)


# Status Handlers
async def handle_get_context_status(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle context service status request."""
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response("Context service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Get service status
        status = context_service.get_status()
        
        return success_response(status, "Context service status retrieved successfully")
        
    except Exception as e:
        logger.error(f"Error handling get context status request: {e}")
        return error_response(f"Error retrieving context status: {str(e)}", ErrorCodes.SERVICE_ERROR)


async def handle_update_context_settings(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle context settings update request."""
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response("Context service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Update settings (this will reload from the database)
        context_service.update_settings()
        
        # Get updated status
        status = context_service.get_status()
        
        return success_response({
            "status": status
        }, "Context settings updated successfully")
        
    except Exception as e:
        logger.error(f"Error handling update context settings request: {e}")
        return error_response(f"Error updating context settings: {str(e)}", ErrorCodes.SERVICE_ERROR)


async def handle_clear_context_cache(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle context cache clear request."""
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response("Context service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Clear cache through the service
        if hasattr(context_service, '_context_cache') and context_service._context_cache:
            context_service._context_cache.clear_cache()
            
            return success_response({}, "Context cache cleared successfully")
        else:
            return error_response("Context cache not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
    except Exception as e:
        logger.error(f"Error handling clear context cache request: {e}")
        return error_response(f"Error clearing context cache: {str(e)}", ErrorCodes.SERVICE_ERROR)


# Utility Functions
def _get_context_service(context: Dict[str, Any]):
    """Get context service from context."""
    try:
        service_container = context.get("service_container")
        if not service_container:
            logger.error("Service container not found in context")
            return None
            
        context_service = service_container.resolve("context")
        if not context_service:
            logger.error("Context service not found in service container")
            return None
            
        return context_service
        
    except Exception as e:
        logger.error(f"Failed to get context service: {e}")
        return None


# Handler Registration
def register_handlers(command_handlers) -> None:
    """Register all context-related command handlers."""
    logger.info("Registering context handlers...")
    
    # Context capture
    command_handlers.register_function("context.capture", handle_capture_context)
    command_handlers.register_function("context.get_recent", handle_get_recent_context)
    command_handlers.register_function("context.search", handle_search_context)
    command_handlers.register_function("context.get_by_application", handle_get_context_by_application)
    
    # Status and management
    command_handlers.register_function("context.get_status", handle_get_context_status)
    command_handlers.register_function("context.update_settings", handle_update_context_settings)
    command_handlers.register_function("context.clear_cache", handle_clear_context_cache)
    
    logger.info("Context handlers registered successfully")
