"""
Context awareness service for <PERSON>.Whispr.

This service orchestrates context capture, processing, and provides API for other components.
It integrates screen capture, OCR, and context analysis to provide contextual information
that can enhance transcription accuracy.
"""

import asyncio
import logging
import time
import concurrent.futures
import hashlib
from typing import Dict, List, Optional, Any
from PIL import Image

from whispr.core.base import BaseService, ServiceContainer
from whispr.config.manager import ConfigurationMixin
from whispr.helpers.context import ScreenCaptureManager, OCRExtractor, ContextAnalyzer, ContextCache, ImageProcessor


class ContextService(BaseService, ConfigurationMixin):
    """Service for managing context awareness functionality."""
    
    def __init__(self, service_container: Optional[ServiceContainer] = None):
        """Initialize the context service.
        
        Args:
            service_container: The service container for dependency resolution
        """
        super().__init__(service_container)
        
        # Core components
        self._screen_capture = ScreenCaptureManager()
        self._ocr_extractor = OCRExtractor()
        self._context_analyzer = ContextAnalyzer()
        self._context_cache = ContextCache()
        self._image_processor = ImageProcessor()
        
        # State
        self._is_enabled = False
        self._is_initialized = False
        self._last_capture_time = 0
        self._capture_in_progress = False
        self._background_task = None
        self._should_stop = False
        self._thread_pool = None
        self._current_capture_future = None
        
        # Configuration
        self._ocr_languages = ['en']
        self._ocr_confidence_threshold = 0.5  # Balanced threshold for speed vs accuracy
        self._cache_ttl = 3600  # 1 hour
        self._capture_interval = 12  # seconds between fallback captures (slightly more frequent)
        self._window_check_interval = 1.5  # seconds between window checks (more responsive)
        self._capture_cooldown = 2  # minimum seconds between captures (more responsive)

        # Performance monitoring
        self._performance_stats = {
            'total_captures': 0,
            'successful_captures': 0,
            'failed_captures': 0,
            'avg_processing_time': 0.0,
            'last_processing_times': [],
            'cache_hits': 0,
            'cache_misses': 0
        }

        # Window tracking for smart capture
        self._last_window_title = None
        self._last_fallback_capture = 0
        
    async def initialize(self) -> bool:
        """Initialize the context service.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            # Call parent initialization
            result = await super().initialize()
            if not result:
                return False
            
            # Load settings to check if context awareness is enabled
            self._load_settings()
            
            if not self._is_enabled:
                self.logger.info("Context awareness is disabled in settings")
                return True
            
            # Initialize components
            if not self._screen_capture.initialize():
                self.logger.error("Failed to initialize screen capture")
                return False
            
            if not self._ocr_extractor.initialize(self._ocr_languages, self._ocr_confidence_threshold):
                self.logger.error("Failed to initialize OCR extractor")
                return False

            # Initialize thread pool for background processing
            self._thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=2, thread_name_prefix="context")

            self._is_initialized = True

            # Start background capture loop
            self._should_stop = False
            self._background_task = asyncio.create_task(self._background_capture_loop())

            self.logger.info("Context service initialized successfully with background capture loop")

            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing context service: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """Clean up the context service.
        
        Returns:
            True if cleanup was successful, False otherwise
        """
        try:
            # Stop background task
            self._should_stop = True
            if self._background_task and not self._background_task.done():
                self._background_task.cancel()
                try:
                    await self._background_task
                except asyncio.CancelledError:
                    pass

            # Clean up components
            if self._screen_capture:
                self._screen_capture.cleanup()

            if self._ocr_extractor:
                self._ocr_extractor.cleanup()

            if self._context_cache:
                self._context_cache.clear_cache()

            if self._image_processor:
                self._image_processor.clear_cache()

            # Cancel any ongoing capture
            if self._current_capture_future and not self._current_capture_future.done():
                self._current_capture_future.cancel()

            # Shutdown thread pool
            if self._thread_pool:
                self._thread_pool.shutdown(wait=True)
                self._thread_pool = None

            self._is_initialized = False
            self.logger.info("Context service cleaned up successfully")

            return await super().cleanup()
            
        except Exception as e:
            self.logger.error(f"Error cleaning up context service: {e}")
            return False
    
    async def capture_context(self, force: bool = False) -> Optional[Dict[str, Any]]:
        """Capture current context (screenshot + OCR + analysis).
        
        Args:
            force: Force capture even if recently captured
            
        Returns:
            Context data or None if capture failed/disabled
        """
        if not self._is_enabled or not self._is_initialized:
            self.logger.debug("Context capture skipped - service disabled or not initialized")
            return None
        
        if self._capture_in_progress:
            self.logger.debug("Context capture already in progress")
            return None
        
        # Check if we captured recently (avoid spam)
        current_time = time.time()
        if not force and (current_time - self._last_capture_time) < 5.0:  # 5 second cooldown
            self.logger.debug("Context capture skipped - too recent")
            return None
        
        try:
            # Cancel any existing capture
            if self._current_capture_future and not self._current_capture_future.done():
                self.logger.debug("Cancelling previous capture in progress")
                self._current_capture_future.cancel()

            self._capture_in_progress = True
            capture_start_time = time.time()
            self.logger.debug("Starting context capture")

            # Run heavy processing in thread pool to avoid blocking event loop
            loop = asyncio.get_event_loop()
            self._current_capture_future = loop.run_in_executor(
                self._thread_pool,
                self._capture_context_sync,
                current_time
            )

            context_analysis = await self._current_capture_future

            if context_analysis:
                capture_end_time = time.time()
                processing_time = capture_end_time - capture_start_time

                # Update performance statistics
                self._update_performance_stats(processing_time, success=True)

                self.logger.info(f"Context captured successfully - relevance: {context_analysis.get('relevance_score', 0.0):.2f}, "
                               f"processing time: {processing_time:.2f}s")

                return context_analysis
            else:
                self._update_performance_stats(0, success=False)
                self.logger.warning("Context capture returned no data")
                return None

        except asyncio.CancelledError:
            self.logger.debug("Context capture was cancelled")
            return None
        except Exception as e:
            self.logger.error(f"Error capturing context: {e}")
            return None
        finally:
            self._capture_in_progress = False
            self._current_capture_future = None
    
    async def get_recent_context(self, limit: int = 5, min_relevance: float = 0.0) -> List[Dict[str, Any]]:
        """Get recent context data.
        
        Args:
            limit: Maximum number of contexts to return
            min_relevance: Minimum relevance score filter
            
        Returns:
            List of recent context data
        """
        if not self._is_enabled:
            return []
        
        try:
            return self._context_cache.get_recent_contexts(limit, min_relevance)
        except Exception as e:
            self.logger.error(f"Error getting recent context: {e}")
            return []
    
    async def search_context(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search for contexts containing specific text.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching context data
        """
        if not self._is_enabled or not query:
            return []
        
        try:
            return self._context_cache.search_contexts(query, limit)
        except Exception as e:
            self.logger.error(f"Error searching context: {e}")
            return []
    
    async def get_context_for_application(self, app_name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get contexts related to a specific application.
        
        Args:
            app_name: Application name to filter by
            limit: Maximum number of results
            
        Returns:
            List of context data for the application
        """
        if not self._is_enabled or not app_name:
            return []
        
        try:
            return self._context_cache.get_contexts_by_application(app_name, limit)
        except Exception as e:
            self.logger.error(f"Error getting context for application: {e}")
            return []
    
    def update_settings(self) -> None:
        """Update service settings from configuration."""
        self._load_settings()
        
        if self._is_enabled and not self._is_initialized:
            # Context awareness was enabled, try to initialize
            asyncio.create_task(self.initialize())
        elif not self._is_enabled and self._is_initialized:
            # Context awareness was disabled, cleanup
            asyncio.create_task(self.cleanup())
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current service status.
        
        Returns:
            A dictionary containing status information
        """
        status = super().get_status()
        
        cache_stats = {}
        if self._context_cache:
            cache_stats = self._context_cache.get_cache_stats()
        
        status.update({
            "enabled": self._is_enabled,
            "initialized": self._is_initialized,
            "capture_in_progress": self._capture_in_progress,
            "last_capture_time": self._last_capture_time,
            "screen_capture_ready": self._screen_capture.is_initialized() if self._screen_capture else False,
            "ocr_ready": self._ocr_extractor.is_initialized() if self._ocr_extractor else False,
            "cache_stats": cache_stats,
            "performance_stats": self._performance_stats.copy(),
            "settings": {
                "ocr_languages": self._ocr_languages,
                "ocr_confidence_threshold": self._ocr_confidence_threshold,
                "capture_interval": self._capture_interval,
                "window_check_interval": self._window_check_interval,
                "capture_cooldown": self._capture_cooldown
            }
        })
        
        return status
    
    def _load_settings(self) -> None:
        """Load settings from configuration."""
        try:
            settings = self.get_settings()

            # Check if context awareness is enabled
            self._is_enabled = settings.get("contextAwareness", False)

            # Load technical configuration from config manager
            config_manager = self.get_service("config")
            context_config = config_manager.get("context") if config_manager else {}

            # Load configuration with defaults from settings.py
            self._ocr_languages = context_config.get("default_ocr_languages", ['en'])
            self._ocr_confidence_threshold = context_config.get("default_ocr_confidence_threshold", 0.5)
            self._cache_ttl = context_config.get("default_cache_ttl", 3600)
            self._capture_interval = context_config.get("default_capture_interval", 15)
            self._window_check_interval = context_config.get("window_check_interval", 2)
            self._capture_cooldown = context_config.get("capture_cooldown_seconds", 3)

            self.logger.debug(f"Context awareness settings loaded - enabled: {self._is_enabled}")

        except Exception as e:
            self.logger.error(f"Error loading context settings: {e}")
            self._is_enabled = False
    
    def _get_active_window_title(self) -> Optional[str]:
        """Get the title of the active window.

        Returns:
            Active window title or None
        """
        try:
            # Try to use pywin32 for Windows
            import win32gui

            # Get the foreground window handle
            hwnd = win32gui.GetForegroundWindow()
            if hwnd:
                # Get the window title
                window_title = win32gui.GetWindowText(hwnd)
                return window_title if window_title else None

        except ImportError:
            # pywin32 not available, fall back to None
            pass
        except Exception as e:
            self.logger.debug(f"Error getting active window title: {e}")

        return None

    async def _background_capture_loop(self) -> None:
        """Smart background loop that captures context based on window title changes and fallback timer.

        Triggers context capture when:
        - Window title changes (app switch, tab change, document change, etc.)
        - Fallback timer expires (ensures we don't miss anything)
        """
        self.logger.info(f"Starting smart context capture loop (window check: {self._window_check_interval}s, fallback: {self._capture_interval}s)")

        while not self._should_stop:
            try:
                # Only run if enabled
                if self._is_enabled and self._is_initialized:
                    current_time = time.time()
                    should_capture = False
                    capture_reason = ""

                    # Check current window title (includes app name, document name, tab title, etc.)
                    current_window = self._get_active_window_title()

                    # Capture on ANY window title change (app switch, tab change, document change)
                    if current_window and current_window != self._last_window_title:
                        # Window title changed - capture immediately (with cooldown)
                        if current_time - self._last_capture_time >= self._capture_cooldown:
                            should_capture = True

                            # Create informative capture reason
                            if self._last_window_title is None:
                                capture_reason = f"initial window: '{current_window}'"
                            else:
                                # Detect type of change
                                if self._is_same_application(self._last_window_title, current_window):
                                    capture_reason = f"content change: '{self._extract_content_name(self._last_window_title)}' -> '{self._extract_content_name(current_window)}'"
                                else:
                                    capture_reason = f"app change: '{self._extract_app_name(self._last_window_title)}' -> '{self._extract_app_name(current_window)}'"

                            self._last_window_title = current_window

                    # Fallback timer - capture even if window hasn't changed (in case we miss something)
                    elif current_time - self._last_fallback_capture >= self._capture_interval:
                        if current_time - self._last_capture_time >= self._capture_cooldown:
                            should_capture = True
                            capture_reason = "fallback timer"
                            self._last_fallback_capture = current_time

                    # Capture if needed
                    if should_capture:
                        self.logger.info(f"Context capture triggered: {capture_reason}")
                        await self.capture_context(force=False)

                # Check window changes frequently to catch tab switches, document changes, etc.
                await asyncio.sleep(self._window_check_interval)

            except asyncio.CancelledError:
                self.logger.info("Smart capture loop cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in smart capture loop: {e}")
                # Continue the loop even if there's an error
                await asyncio.sleep(5)  # Short delay before retrying

        self.logger.info("Smart context capture loop stopped")

    def _is_same_application(self, title1: str, title2: str) -> bool:
        """Check if two window titles are from the same application.

        Args:
            title1: First window title
            title2: Second window title

        Returns:
            True if same application, False otherwise
        """
        if not title1 or not title2:
            return False

        # Extract app names (usually after the last " - ")
        app1 = self._extract_app_name(title1)
        app2 = self._extract_app_name(title2)

        return app1.lower() == app2.lower()

    def _extract_app_name(self, window_title: str) -> str:
        """Extract application name from window title.

        Args:
            window_title: Full window title

        Returns:
            Application name
        """
        if not window_title:
            return ""

        # Most apps use "Content - Application Name" format
        if " - " in window_title:
            return window_title.split(" - ")[-1]

        return window_title

    def _extract_content_name(self, window_title: str) -> str:
        """Extract content name (document, tab, etc.) from window title.

        Args:
            window_title: Full window title

        Returns:
            Content name
        """
        if not window_title:
            return ""

        # Most apps use "Content - Application Name" format
        if " - " in window_title:
            return window_title.split(" - ")[0]

        return window_title

    def _capture_context_sync(self, capture_time: float) -> Optional[Dict[str, Any]]:
        """Synchronous context capture method that runs in thread pool.

        Args:
            capture_time: Time when capture was initiated

        Returns:
            Context analysis data or None if failed
        """
        try:
            screenshot_start = time.time()

            # Capture screenshot (active window for better performance)
            screenshot = self._screen_capture.capture_screen()
            if not screenshot:
                self.logger.warning("Failed to capture screenshot")
                return None

            # Get window title for application-aware processing
            window_title = self._get_active_window_title()

            # Application-aware image optimization
            if self._is_text_heavy_application(window_title):
                self.logger.debug("Detected text-heavy application, applying specialized optimization")
                screenshot = self._image_processor.optimize_for_terminal_content(screenshot)
            else:
                # Standard optimization for other applications
                screenshot = self._image_processor.optimize_for_ocr(screenshot)

            # Early exit if image should be skipped for performance reasons
            if self._image_processor.should_skip_ocr(screenshot):
                self.logger.debug("Skipping OCR for performance reasons (image too small/large)")
                return None

            screenshot_end = time.time()
            screenshot_time = screenshot_end - screenshot_start

            # Check if this image is identical to the last one (skip OCR if so)
            image_hash = self._image_processor.calculate_image_hash(screenshot)
            cached_result = self._image_processor.check_cache(image_hash)

            if cached_result:
                ocr_data = cached_result
                ocr_time = 0.0  # No OCR processing needed
                self._performance_stats['cache_hits'] += 1
                self.logger.debug("Using cached OCR result for identical image")
            else:
                self._performance_stats['cache_misses'] += 1
                # Extract text using OCR
                ocr_start = time.time()
                ocr_data = self._ocr_extractor.extract_text(screenshot)
                if not ocr_data or not ocr_data.get("text"):
                    self.logger.debug("No text extracted from screenshot")
                    # Still return context data even without text
                    ocr_data = {"text": "", "words": [], "confidence": 0.0}
                else:
                    # Log extracted text elements
                    self._log_ocr_results(ocr_data)

                ocr_end = time.time()
                ocr_time = ocr_end - ocr_start

                # Cache the result
                self._image_processor.cache_result(image_hash, ocr_data)



            # Get window title
            window_title = self._get_active_window_title()

            # Analyze context
            analysis_start = time.time()
            context_analysis = self._context_analyzer.analyze_context(ocr_data, window_title)
            analysis_end = time.time()
            analysis_time = analysis_end - analysis_start

            # Log context analysis results
            self._log_context_analysis(context_analysis, window_title)

            # Store in cache
            cache_key = self._context_cache.store_context(context_analysis, self._cache_ttl)

            # Update capture time
            self._last_capture_time = capture_time

            # Add cache key and timing info to the analysis
            context_analysis["cache_key"] = cache_key
            context_analysis["timing"] = {
                "screenshot": f"{screenshot_time:.3f}s",
                "ocr": f"{ocr_time:.3f}s",
                "analysis": f"{analysis_time:.3f}s",
                "total": f"{(analysis_end - screenshot_start):.3f}s"
            }

            self.logger.debug(f"Context processing times - Screenshot: {screenshot_time:.3f}s, "
                            f"OCR: {ocr_time:.3f}s, Analysis: {analysis_time:.3f}s")

            return context_analysis

        except Exception as e:
            self.logger.error(f"Error in synchronous context capture: {e}")
            return None

    def _log_context_analysis(self, context_analysis: Dict[str, Any], window_title: Optional[str]) -> None:
        """Log the results of context analysis for debugging.

        Args:
            context_analysis: The analyzed context data
            window_title: Current window title
        """
        try:
            relevance = context_analysis.get("relevance_score", 0.0)

            # Log window context
            if window_title:
                self.logger.info(f"Active window: '{window_title}'")

            # Log detected applications
            applications = context_analysis.get("applications", [])
            if applications:
                app_names = [f"{app.get('name')} ({app.get('confidence', 0):.2f})" for app in applications[:3]]
                self.logger.info(f"Detected applications: {app_names}")

            # Log keywords
            keywords = context_analysis.get("keywords", [])
            if keywords:
                keyword_list = [f"{kw.get('keyword')} ({kw.get('score', 0):.2f})" for kw in keywords[:5]]
                self.logger.info(f"Top keywords: {keyword_list}")

            # Log technical terms
            tech_terms = context_analysis.get("technical_terms", [])
            if tech_terms:
                term_categories = {}
                for term in tech_terms:
                    category = term.get("category", "unknown")
                    if category not in term_categories:
                        term_categories[category] = []
                    term_categories[category].append(term.get("term", ""))

                for category, terms in term_categories.items():
                    self.logger.debug(f"Technical terms ({category}): {terms[:5]}")

            # Log high-value entities for transcription
            entities = context_analysis.get("entities", {})

            # Prioritize logging of transcription-relevant entities
            high_value_entities = ["social_mentions", "proper_nouns", "technical_terms", "domain_names", "version_numbers"]
            for entity_type in high_value_entities:
                entity_list = entities.get(entity_type, [])
                if entity_list:
                    self.logger.info(f"Found {entity_type}: {entity_list[:5]}")

            # Log other entities at debug level
            other_entities = {k: v for k, v in entities.items() if k not in high_value_entities and v}
            for entity_type, entity_list in other_entities.items():
                if entity_list:
                    self.logger.debug(f"Entities ({entity_type}): {entity_list[:3]}")

            # Log overall relevance with more context
            self.logger.info(f"Context relevance score: {relevance:.2f} (transcription value: {'HIGH' if relevance > 0.5 else 'MEDIUM' if relevance > 0.2 else 'LOW'})")

        except Exception as e:
            self.logger.error(f"Error logging context analysis: {e}")

    def _log_ocr_results(self, ocr_data: Dict[str, Any]) -> None:
        """Log OCR extraction results.

        Args:
            ocr_data: OCR result data
        """
        try:
            extracted_text = ocr_data.get("text", "").strip()
            word_count = ocr_data.get("word_count", 0)
            confidence = ocr_data.get("confidence", 0.0)

            if extracted_text:
                # Truncate very long text for logging
                display_text = extracted_text[:200] + "..." if len(extracted_text) > 200 else extracted_text
                self.logger.info(f"OCR extracted {word_count} words (confidence: {confidence:.2f}): '{display_text}'")

                # Also log individual words with high confidence
                words = ocr_data.get("words", [])
                high_confidence_words = [w for w in words if w.get("confidence", 0) > 0.8]
                if high_confidence_words:
                    word_texts = [w.get("text", "") for w in high_confidence_words[:10]]  # Show first 10
                    self.logger.debug(f"High confidence words: {word_texts}")
            else:
                self.logger.debug("OCR completed but no readable text found")
        except Exception as e:
            self.logger.error(f"Error logging OCR results: {e}")

    def _update_performance_stats(self, processing_time: float, success: bool) -> None:
        """Update performance statistics.

        Args:
            processing_time: Time taken for processing
            success: Whether the capture was successful
        """
        try:
            self._performance_stats['total_captures'] += 1

            if success:
                self._performance_stats['successful_captures'] += 1

                # Update processing time statistics
                times = self._performance_stats['last_processing_times']
                times.append(processing_time)

                # Keep only last 20 processing times for rolling average
                if len(times) > 20:
                    times.pop(0)

                # Calculate average processing time
                if times:
                    self._performance_stats['avg_processing_time'] = sum(times) / len(times)
            else:
                self._performance_stats['failed_captures'] += 1

        except Exception as e:
            self.logger.error(f"Error updating performance stats: {e}")

    def _is_text_heavy_application(self, window_title: Optional[str]) -> bool:
        """Check if the current application is likely to have lots of text (terminal, editor, etc.).

        Args:
            window_title: Current window title

        Returns:
            True if application is text-heavy
        """
        if not window_title:
            return False

        window_title_lower = window_title.lower()

        # Text editors and IDEs
        text_heavy_apps = [
            'cursor', 'vscode', 'visual studio code', 'sublime', 'atom', 'notepad++',
            'vim', 'emacs', 'nano', 'intellij', 'pycharm', 'webstorm', 'phpstorm',
            'eclipse', 'netbeans', 'brackets', 'code', 'rider', 'clion'
        ]

        # Terminals and command line tools
        terminal_apps = [
            'terminal', 'cmd', 'powershell', 'bash', 'zsh', 'fish', 'command prompt',
            'windows terminal', 'hyper', 'iterm', 'konsole', 'gnome-terminal',
            'xterm', 'putty', 'mobaxterm'
        ]

        # Development tools with lots of text
        dev_tools = [
            'git', 'github', 'gitlab', 'bitbucket', 'docker', 'kubernetes',
            'postman', 'insomnia', 'swagger', 'api', 'database', 'sql',
            'mongodb', 'redis', 'elasticsearch'
        ]

        all_text_heavy = text_heavy_apps + terminal_apps + dev_tools

        return any(app in window_title_lower for app in all_text_heavy)
