"""
Context analysis functionality for processing extracted text and identifying
relevant contextual information for transcription enhancement.
"""

import logging
import re
import time
from typing import Dict, List, Optional, Set, Any
from collections import Counter


class ContextAnalyzer:
    """Analyzes extracted context data to identify relevant information."""
    
    def __init__(self):
        """Initialize the context analyzer."""
        self.logger = logging.getLogger("whispr.context.analyzer")
        self._application_patterns = self._build_application_patterns()
        self._technical_patterns = self._build_technical_patterns()
        
    def analyze_context(self, ocr_data: Dict[str, Any], window_title: Optional[str] = None) -> Dict[str, Any]:
        """Analyze context data and extract relevant information.
        
        Args:
            ocr_data: OCR extraction results
            window_title: Optional window title for additional context
            
        Returns:
            Analyzed context data with categorized information
        """
        try:
            text = ocr_data.get("text", "")
            words = ocr_data.get("words", [])

            # Debug logging to understand what OCR is providing
            word_count = len(words) if words else 0
            self.logger.debug(f"Context analysis input: {word_count} words, text length: {len(text)}")
            if words:
                sample_words = [w.get("text", "") for w in words[:10]]  # First 10 words
                self.logger.debug(f"Sample words from OCR: {sample_words}")

                # Log transcription-relevant words (proper nouns, technical terms, etc.)
                transcription_relevant = []
                for word_data in words:
                    word_text = word_data.get("text", "")
                    if self._is_transcription_relevant(word_text):
                        transcription_relevant.append(word_text)

                if transcription_relevant:
                    self.logger.info(f"Found {len(transcription_relevant)} transcription-relevant terms: {transcription_relevant[:10]}")
                else:
                    self.logger.debug("No transcription-relevant terms found in OCR results")

            analysis = {
                "timestamp": time.time(),
                "raw_text": text,
                "word_count": len(words),
                "confidence": ocr_data.get("confidence", 0.0),
                "applications": self._detect_applications(text, window_title),
                "technical_terms": self._extract_technical_terms(text),
                "keywords": self._extract_context_keywords(text),
                "entities": self._extract_entities(text),
                "window_context": self._analyze_window_context(window_title) if window_title else {},
                "relevance_score": 0.0
            }
            
            # Calculate relevance score
            analysis["relevance_score"] = self._calculate_relevance_score(analysis)
            
            self.logger.debug(f"Context analysis completed with relevance score: {analysis['relevance_score']:.2f}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing context: {e}")
            return self._empty_analysis()
    
    def _detect_applications(self, text: str, window_title: Optional[str] = None) -> List[Dict[str, Any]]:
        """Detect applications and software mentioned in the text.
        
        Args:
            text: Text to analyze
            window_title: Optional window title
            
        Returns:
            List of detected applications with confidence scores
        """
        applications = []
        text_lower = text.lower()
        
        # Check window title first
        if window_title:
            for app_name, patterns in self._application_patterns.items():
                if any(pattern in window_title.lower() for pattern in patterns):
                    applications.append({
                        "name": app_name,
                        "confidence": 0.9,
                        "source": "window_title"
                    })
        
        # Check text content
        for app_name, patterns in self._application_patterns.items():
            matches = sum(1 for pattern in patterns if pattern in text_lower)
            if matches > 0:
                confidence = min(0.8, matches * 0.3)  # Max 0.8 for text matches
                applications.append({
                    "name": app_name,
                    "confidence": confidence,
                    "source": "text_content"
                })
        
        # Remove duplicates and sort by confidence
        unique_apps = {}
        for app in applications:
            name = app["name"]
            if name not in unique_apps or app["confidence"] > unique_apps[name]["confidence"]:
                unique_apps[name] = app
        
        return sorted(unique_apps.values(), key=lambda x: x["confidence"], reverse=True)
    
    def _extract_technical_terms(self, text: str) -> List[Dict[str, Any]]:
        """Extract technical terms and jargon from the text.
        
        Args:
            text: Text to analyze
            
        Returns:
            List of technical terms with categories
        """
        technical_terms = []
        text_lower = text.lower()
        
        for category, patterns in self._technical_patterns.items():
            for pattern in patterns:
                matches = re.finditer(r'\b' + re.escape(pattern) + r'\b', text_lower)
                for match in matches:
                    technical_terms.append({
                        "term": pattern,
                        "category": category,
                        "position": match.start()
                    })
        
        # Remove duplicates
        unique_terms = {}
        for term in technical_terms:
            key = (term["term"], term["category"])
            if key not in unique_terms:
                unique_terms[key] = term
        
        return list(unique_terms.values())
    
    def _extract_context_keywords(self, text: str) -> List[Dict[str, Any]]:
        """Extract contextually relevant keywords for transcription assistance.

        Args:
            text: Text to analyze

        Returns:
            List of keywords with relevance scores
        """
        if not text:
            return []

        # Extract words with better pattern matching
        words = re.findall(r'\b[a-zA-Z][a-zA-Z0-9]*\b', text)  # Keep original case

        # Filter and score words
        keyword_scores = {}
        for word in words:
            if self._is_transcription_relevant_keyword(word):
                score = self._calculate_transcription_keyword_score(word, text)
                if score > 0.2:  # Higher threshold for better quality
                    keyword_scores[word.lower()] = max(keyword_scores.get(word.lower(), 0), score)

        # Convert to list and sort by score
        keywords = [
            {"keyword": word, "score": score}
            for word, score in keyword_scores.items()
        ]

        return sorted(keywords, key=lambda x: x["score"], reverse=True)[:20]  # More keywords for better context

    def _is_transcription_relevant_keyword(self, word: str) -> bool:
        """Check if a word is relevant for transcription enhancement.

        Args:
            word: Word to check

        Returns:
            True if word is transcription-relevant
        """
        if len(word) < 3:
            return False

        word_lower = word.lower()

        # Skip very common stop words
        stop_words = {
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
            'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy',
            'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'this', 'that',
            'with', 'have', 'from', 'they', 'know', 'want', 'been', 'good', 'much',
            'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long',
            'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were'
        }

        if word_lower in stop_words:
            return False

        # Prioritize proper nouns (capitalized words)
        if word[0].isupper() and len(word) > 3:
            return True

        # Technical terms and brand names
        if any(char.isupper() for char in word[1:]):  # CamelCase
            return True

        # Words with numbers (version numbers, model names, etc.)
        if any(char.isdigit() for char in word):
            return True

        # Longer words are often more specific and useful
        if len(word) > 6:
            return True

        # Domain-specific terms
        domain_indicators = [
            'api', 'app', 'web', 'net', 'tech', 'dev', 'pro', 'max', 'plus', 'lite',
            'beta', 'alpha', 'demo', 'test', 'live', 'prod', 'staging', 'local'
        ]

        if any(indicator in word_lower for indicator in domain_indicators):
            return True

        return len(word) >= 4  # Default threshold for medium-length words

    def _calculate_transcription_keyword_score(self, word: str, text: str) -> float:
        """Calculate relevance score for a keyword in transcription context.

        Args:
            word: Word to score
            text: Full text context

        Returns:
            Relevance score between 0.0 and 1.0
        """
        score = 0.3  # Base score

        # Proper noun bonus (capitalized words are often names/brands)
        if word[0].isupper():
            score += 0.3

        # CamelCase bonus (technical terms)
        if any(char.isupper() for char in word[1:]):
            score += 0.2

        # Length bonus (longer words are often more specific)
        if len(word) > 8:
            score += 0.2
        elif len(word) > 6:
            score += 0.1

        # Numbers in word bonus (versions, models, etc.)
        if any(char.isdigit() for char in word):
            score += 0.15

        # Frequency penalty (very common words are less useful)
        frequency = text.lower().count(word.lower())
        if frequency > 5:
            score -= 0.2
        elif frequency > 3:
            score -= 0.1

        # Context bonus for technical/brand terms
        word_lower = word.lower()
        if any(term in word_lower for term in ['tech', 'app', 'api', 'web', 'net', 'pro', 'max']):
            score += 0.1

        return max(0.0, min(1.0, score))
    
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract named entities from the text, focusing on transcription-relevant content.

        Args:
            text: Text to analyze

        Returns:
            Dictionary of entity types and their values
        """
        entities = {
            "social_mentions": [],      # @usernames, #hashtags
            "emails": [],
            "urls": [],
            "file_paths": [],
            "numbers": [],
            "dates": [],
            "proper_nouns": [],         # Capitalized words (names, places, brands)
            "technical_terms": [],      # CamelCase, snake_case, etc.
            "version_numbers": [],      # v1.2.3, 2.0.1, etc.
            "domain_names": []          # website.com, api.service.io
        }

        # Social media mentions and hashtags (high priority for transcription)
        social_pattern = r'[@#][A-Za-z0-9_]+'
        entities["social_mentions"] = re.findall(social_pattern, text)

        # Email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        entities["emails"] = re.findall(email_pattern, text)

        # URLs and domain names
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        entities["urls"] = re.findall(url_pattern, text)

        # Domain names without protocol
        domain_pattern = r'\b[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:\.[a-zA-Z]{2,})?\b'
        potential_domains = re.findall(domain_pattern, text)
        # Filter out common false positives
        entities["domain_names"] = [d for d in potential_domains if not d.endswith(('.jpg', '.png', '.pdf', '.doc'))]

        # File paths (Windows and Unix style)
        file_pattern = r'[A-Za-z]:\\[^\s<>"|?*]+|/[^\s<>"|?*]+'
        entities["file_paths"] = re.findall(file_pattern, text)

        # Version numbers (important for technical contexts)
        version_pattern = r'\bv?\d+\.\d+(?:\.\d+)?(?:-[a-zA-Z0-9]+)?\b'
        entities["version_numbers"] = re.findall(version_pattern, text)

        # Proper nouns (capitalized words - names, places, brands)
        proper_noun_pattern = r'\b[A-Z][a-z]{2,}\b'
        potential_proper_nouns = re.findall(proper_noun_pattern, text)
        # Filter out common words that are often capitalized
        common_words = {'The', 'This', 'That', 'These', 'Those', 'And', 'But', 'Or', 'For', 'With', 'By', 'From', 'To', 'In', 'On', 'At'}
        entities["proper_nouns"] = [word for word in potential_proper_nouns if word not in common_words]

        # Technical terms (CamelCase, snake_case, kebab-case)
        camel_case_pattern = r'\b[a-z]+[A-Z][a-zA-Z]*\b'
        snake_case_pattern = r'\b[a-z]+_[a-z_]+\b'
        kebab_case_pattern = r'\b[a-z]+-[a-z-]+\b'

        entities["technical_terms"].extend(re.findall(camel_case_pattern, text))
        entities["technical_terms"].extend(re.findall(snake_case_pattern, text))
        entities["technical_terms"].extend(re.findall(kebab_case_pattern, text))

        # Numbers (including decimals) - but filter out very common ones
        number_pattern = r'\b\d+(?:\.\d+)?\b'
        all_numbers = re.findall(number_pattern, text)
        # Keep numbers that might be relevant (not single digits, not years unless recent)
        entities["numbers"] = [num for num in all_numbers if len(num) > 1 and not (len(num) == 4 and num.startswith('19'))]

        # Date patterns
        date_pattern = r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b'
        entities["dates"] = re.findall(date_pattern, text)

        return entities
    
    def _analyze_window_context(self, window_title: str) -> Dict[str, Any]:
        """Analyze window title for additional context.
        
        Args:
            window_title: Window title to analyze
            
        Returns:
            Window context information
        """
        if not window_title:
            return {}
            
        return {
            "title": window_title,
            "application": self._extract_app_from_title(window_title),
            "document_name": self._extract_document_name(window_title),
            "is_browser": "browser" in window_title.lower() or any(
                browser in window_title.lower() 
                for browser in ["chrome", "firefox", "edge", "safari"]
            )
        }
    
    def _calculate_relevance_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate overall relevance score for transcription assistance.

        Args:
            analysis: Context analysis data

        Returns:
            Relevance score between 0.0 and 1.0
        """
        score = 0.0

        # Base score from OCR confidence (reduced weight)
        score += analysis.get("confidence", 0.0) * 0.1

        # Application detection bonus (important for context)
        apps = analysis.get("applications", [])
        if apps:
            max_app_confidence = max(app["confidence"] for app in apps)
            score += max_app_confidence * 0.2

        # High-value entities for transcription (major boost)
        entities = analysis.get("entities", {})

        # Social mentions are extremely valuable for transcription (@usernames, #hashtags)
        social_mentions = entities.get("social_mentions", [])
        score += min(len(social_mentions) * 0.15, 0.4)  # Up to 40% boost

        # Proper nouns are very valuable (names, places, brands)
        proper_nouns = entities.get("proper_nouns", [])
        score += min(len(proper_nouns) * 0.08, 0.3)  # Up to 30% boost

        # Technical terms are valuable for technical contexts
        technical_terms = entities.get("technical_terms", [])
        score += min(len(technical_terms) * 0.05, 0.2)  # Up to 20% boost

        # Domain names and URLs are valuable
        domains = entities.get("domain_names", [])
        urls = entities.get("urls", [])
        score += min((len(domains) + len(urls)) * 0.06, 0.15)

        # Version numbers are valuable in technical contexts
        versions = entities.get("version_numbers", [])
        score += min(len(versions) * 0.08, 0.15)

        # Email addresses are valuable
        emails = entities.get("emails", [])
        score += min(len(emails) * 0.1, 0.15)

        # Technical terms from analysis
        tech_terms = analysis.get("technical_terms", [])
        score += min(len(tech_terms) * 0.03, 0.15)

        # Keywords bonus (reduced weight since entities are more important)
        keywords = analysis.get("keywords", [])
        if keywords:
            # Focus on high-scoring keywords
            high_value_keywords = [kw for kw in keywords if kw.get("score", 0) > 0.6]
            score += min(len(high_value_keywords) * 0.04, 0.1)

        # Bonus for having any transcription-relevant content
        has_transcription_content = (
            len(social_mentions) > 0 or
            len(proper_nouns) > 0 or
            len(technical_terms) > 0 or
            len(domains) > 0 or
            len(versions) > 0
        )
        if has_transcription_content:
            score += 0.1  # Base relevance bonus

        return min(score, 1.0)
    
    def _build_application_patterns(self) -> Dict[str, List[str]]:
        """Build patterns for application detection."""
        return {
            "Microsoft Word": ["word", "microsoft word", "winword", "docx", "doc"],
            "Microsoft Excel": ["excel", "microsoft excel", "xlsx", "xls", "spreadsheet"],
            "Microsoft PowerPoint": ["powerpoint", "microsoft powerpoint", "pptx", "ppt", "presentation"],
            "Google Chrome": ["chrome", "google chrome", "chromium"],
            "Mozilla Firefox": ["firefox", "mozilla"],
            "Microsoft Edge": ["edge", "microsoft edge", "msedge"],
            "Safari": ["safari"],
            "Visual Studio Code": ["visual studio code", "vscode", "code", "vs code"],
            "Visual Studio": ["visual studio", "devenv"],
            "IntelliJ IDEA": ["intellij", "idea", "jetbrains"],
            "PyCharm": ["pycharm"],
            "Sublime Text": ["sublime", "sublime text"],
            "Atom": ["atom"],
            "Notepad++": ["notepad++", "notepad plus"],
            "Notepad": ["notepad"],
            "Adobe Acrobat": ["acrobat", "adobe acrobat", "pdf"],
            "Adobe Photoshop": ["photoshop", "adobe photoshop"],
            "Adobe Illustrator": ["illustrator", "adobe illustrator"],
            "Figma": ["figma"],
            "Sketch": ["sketch"],
            "Slack": ["slack"],
            "Discord": ["discord"],
            "WhatsApp": ["whatsapp"],
            "Telegram": ["telegram"],
            "Signal": ["signal"],
            "Zoom": ["zoom"],
            "Teams": ["microsoft teams", "teams"],
            "Skype": ["skype"],
            "Google Meet": ["meet", "google meet"],
            "Outlook": ["outlook", "microsoft outlook"],
            "Gmail": ["gmail", "google mail"],
            "Thunderbird": ["thunderbird", "mozilla thunderbird"],
            "Spotify": ["spotify"],
            "YouTube": ["youtube"],
            "Netflix": ["netflix"],
            "Twitch": ["twitch"],
            "Steam": ["steam"],
            "Epic Games": ["epic", "epic games"],
            "Terminal": ["terminal", "cmd", "command prompt", "powershell", "bash"],
            "File Explorer": ["explorer", "file explorer", "finder"],
            "Calculator": ["calculator", "calc"],
            "Task Manager": ["task manager", "taskmgr"],
            "Control Panel": ["control panel"],
            "Settings": ["settings", "system settings"]
        }
    
    def _build_technical_patterns(self) -> Dict[str, List[str]]:
        """Build patterns for technical term detection."""
        return {
            "programming": [
                "function", "variable", "class", "method", "array", "object",
                "string", "integer", "boolean", "null", "undefined", "return",
                "import", "export", "const", "let", "var", "if", "else", "for",
                "while", "try", "catch", "async", "await", "promise", "callback",
                "closure", "prototype", "inheritance", "polymorphism", "encapsulation",
                "algorithm", "recursion", "iteration", "debugging", "refactoring"
            ],
            "web": [
                "html", "css", "javascript", "react", "vue", "angular", "svelte",
                "node", "npm", "webpack", "vite", "api", "rest", "graphql", "json", "xml",
                "http", "https", "url", "domain", "server", "client", "frontend", "backend",
                "middleware", "cors", "authentication", "authorization", "jwt", "oauth",
                "responsive", "bootstrap", "tailwind", "sass", "scss", "typescript"
            ],
            "database": [
                "sql", "mysql", "postgresql", "mongodb", "database", "table", "collection",
                "query", "select", "insert", "update", "delete", "join", "index", "schema",
                "migration", "transaction", "acid", "nosql", "redis", "elasticsearch",
                "orm", "odm", "prisma", "sequelize", "mongoose", "aggregation"
            ],
            "system": [
                "windows", "linux", "macos", "terminal", "command", "shell",
                "bash", "powershell", "registry", "service", "process", "daemon",
                "container", "docker", "kubernetes", "deployment", "ci", "cd",
                "pipeline", "automation", "monitoring", "logging", "metrics"
            ],
            "cloud": [
                "aws", "azure", "gcp", "cloud", "serverless", "lambda", "ec2", "s3",
                "cloudfront", "vpc", "iam", "kubernetes", "terraform", "ansible",
                "microservices", "devops", "infrastructure", "scaling", "load balancer"
            ],
            "ai_ml": [
                "machine learning", "artificial intelligence", "neural network", "deep learning",
                "tensorflow", "pytorch", "scikit-learn", "pandas", "numpy", "matplotlib",
                "model", "training", "inference", "dataset", "feature", "algorithm",
                "regression", "classification", "clustering", "nlp", "computer vision"
            ]
        }
    

    
    def _extract_app_from_title(self, title: str) -> Optional[str]:
        """Extract application name from window title."""
        # Common patterns for extracting app names
        if " - " in title:
            return title.split(" - ")[-1]
        elif " | " in title:
            return title.split(" | ")[-1]
        return None
    
    def _extract_document_name(self, title: str) -> Optional[str]:
        """Extract document name from window title."""
        if " - " in title:
            return title.split(" - ")[0]
        elif " | " in title:
            return title.split(" | ")[0]
        return title
    
    def _empty_analysis(self) -> Dict[str, Any]:
        """Return empty analysis structure."""
        return {
            "timestamp": time.time(),
            "raw_text": "",
            "word_count": 0,
            "confidence": 0.0,
            "applications": [],
            "technical_terms": [],
            "keywords": [],
            "entities": {},
            "window_context": {},
            "relevance_score": 0.0
        }

    def _is_transcription_relevant(self, word: str) -> bool:
        """Check if a word is relevant for transcription enhancement.

        Args:
            word: Word to check

        Returns:
            True if word is transcription-relevant
        """
        if len(word) < 3:
            return False

        # Patterns that indicate transcription-relevant content
        transcription_patterns = [
            r'^[A-Z][a-z]+$',                    # Proper nouns (capitalized words)
            r'^[A-Z]{2,}$',                      # Acronyms (all caps)
            r'.*\.(com|org|net|edu|gov)$',       # Websites
            r'^[a-z]+[A-Z][a-z]*$',              # CamelCase
            r'^[a-z]+_[a-z]+$',                  # snake_case
            r'^\d+[A-Za-z]+$',                   # Alphanumeric codes
            r'^[A-Za-z]+\d+$',                   # Words with numbers
        ]

        # Check against patterns
        for pattern in transcription_patterns:
            if re.match(pattern, word):
                return True

        # Check against common technical/domain terms
        technical_indicators = [
            # Programming languages
            'python', 'javascript', 'typescript', 'java', 'csharp', 'cpp', 'rust', 'go', 'php', 'ruby', 'swift', 'kotlin',
            # Web technologies
            'api', 'json', 'xml', 'http', 'https', 'sql', 'css', 'html', 'react', 'vue', 'angular', 'node', 'express',
            # Cloud & DevOps
            'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'terraform', 'ansible', 'jenkins', 'gitlab', 'github',
            # Databases
            'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'sqlite', 'oracle', 'cassandra',
            # Companies & Brands
            'google', 'microsoft', 'apple', 'meta', 'facebook', 'amazon', 'netflix', 'spotify', 'adobe', 'salesforce',
            # Communication platforms
            'linkedin', 'twitter', 'instagram', 'youtube', 'discord', 'slack', 'zoom', 'teams', 'skype', 'whatsapp',
            # Software & Tools
            'outlook', 'gmail', 'chrome', 'firefox', 'safari', 'edge', 'vscode', 'intellij', 'photoshop', 'figma',
            # AI & ML
            'tensorflow', 'pytorch', 'openai', 'chatgpt', 'claude', 'gemini', 'copilot', 'huggingface',
            # Business terms
            'saas', 'paas', 'iaas', 'crm', 'erp', 'roi', 'kpi', 'mvp', 'poc', 'b2b', 'b2c', 'api', 'sdk',
            # File formats
            'pdf', 'docx', 'xlsx', 'pptx', 'csv', 'json', 'xml', 'yaml', 'md', 'txt'
        ]

        if word.lower() in technical_indicators:
            return True

        # Check for common file extensions
        if word.lower().endswith(('.com', '.org', '.net', '.edu', '.gov', '.io', '.ai', '.co', '.app')):
            return True

        # Check for version numbers (e.g., v1.2.3, 2.0.1)
        if re.match(r'^v?\d+\.\d+(\.\d+)?$', word.lower()):
            return True

        # Words longer than 6 characters are often domain-specific
        if len(word) > 6:
            return True

        return False
