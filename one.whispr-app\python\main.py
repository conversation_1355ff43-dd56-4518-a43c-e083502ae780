#!/usr/bin/env python
"""
Main entry point for the One.Whispr Python backend.

This script initializes and starts the One.Whispr application.
"""

import argparse
import asyncio
import os
import sys
from typing import Dict, Any, Optional

# Add project directory to path
# Handle both regular and separated build scenarios
current_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(current_dir)

# For separated builds, the whispr module might be in the same directory as main.py
if os.path.exists(os.path.join(current_dir, 'whispr')):
    sys.path.insert(0, current_dir)
else:
    sys.path.insert(0, project_dir)

from whispr.core.application import create_application


def parse_arguments():
    """Parse command line arguments.
    
    Returns:
        The parsed arguments
    """
    parser = argparse.ArgumentParser(description='One.Whispr Python backend')
    parser.add_argument('--port', type=int, default=0,
                      help='WebSocket server port (default: 0, auto-select available port)')
    parser.add_argument('--host', type=str, default='127.0.0.1',
                      help='WebSocket server host (default: 127.0.0.1)')
    parser.add_argument('--port-file', type=str,
                      help='File to write the actual port number to')
    parser.add_argument('--config', type=str,
                      help='Path to configuration file')
    parser.add_argument('--log-level', type=str, default='INFO',
                      choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                      help='Logging level (default: INFO)')
    return parser.parse_args()


async def main():
    """Main entry point.
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    args = parse_arguments()
    
    # Print startup info to stdout for debugging
    print(f"Starting One.Whispr Python backend...", flush=True)
    print(f"Host: {args.host}, Port: {args.port}, Log Level: {args.log_level}", flush=True)

    # Initialize ML libraries early to avoid delays later
    try:
        from whispr.helpers.models.model_loader import _initialize_libraries
        _initialize_libraries()
    except Exception as e:
        print(f"Warning: Failed to start background ML library initialization: {e}", flush=True)
        # Continue anyway - libraries will be initialized on first use

    # Set environment variables for configuration
    os.environ['WHISPR_HOST'] = args.host
    os.environ['WHISPR_PORT'] = str(args.port)
    os.environ['WHISPR_LOG_LEVEL'] = args.log_level

    # Create and initialize application
    app = create_application(args.config)
    
    # Run the application
    try:
        await app.run(args.port_file)
        return 0
    except Exception as e:
        print(f"Error running application: {e}", file=sys.stderr)
        return 1


if __name__ == '__main__':
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("Application terminated by user")
        sys.exit(0)
    except Exception as e:
        print(f"Unhandled exception: {e}", file=sys.stderr)
        sys.exit(1) 