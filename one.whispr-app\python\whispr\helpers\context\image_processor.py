"""
Image processing utilities for context awareness.

This module handles image optimization, caching, and preprocessing for OCR.
"""

import hashlib
import logging
import time
from typing import Optional, Dict, Any, Tuple
from PIL import Image


class ImageProcessor:
    """Handles image processing and caching for context awareness."""
    
    def __init__(self):
        """Initialize the image processor."""
        self.logger = logging.getLogger(__name__)
        self._last_image_hash = None
        self._last_ocr_result = None
        
    def optimize_for_ocr(self, image: Image.Image) -> Image.Image:
        """Optimize image for better OCR processing with enhanced preprocessing.

        Args:
            image: Original PIL Image

        Returns:
            Optimized PIL Image
        """
        try:
            original_size = image.size

            # Early exit for extremely large images that would be very slow
            if max(image.size) > 5000:
                self.logger.warning(f"Image too large ({original_size}), may be slow to process")

            # Optimized max dimension for speed vs quality balance
            max_dimension = 1200  # Further reduced for faster OCR processing
            if max(image.size) > max_dimension:
                # Calculate scaling factor
                scale = max_dimension / max(image.size)
                new_width = int(image.width * scale)
                new_height = int(image.height * scale)

                # Use highest quality resampling to preserve text clarity
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                self.logger.debug(f"High-quality resize for text clarity: {original_size} -> {image.size}")
            else:
                self.logger.debug(f"No resize needed, keeping original size: {original_size}")

            # Convert to RGB for better OCR compatibility
            if image.mode != 'RGB':
                image = image.convert('RGB')
                self.logger.debug("Converted image to RGB for OCR processing")

            # Skip image enhancement for speed - OCR works well with clean screenshots

            return image

        except Exception as e:
            self.logger.error(f"Error optimizing image for OCR: {e}")
            return image

    def _enhance_text_clarity(self, image: Image.Image) -> Image.Image:
        """Apply subtle enhancements to improve text clarity for OCR.

        Args:
            image: PIL Image to enhance

        Returns:
            Enhanced PIL Image
        """
        try:
            from PIL import ImageEnhance, ImageFilter

            # Subtle sharpening to improve text edges
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)  # Very subtle sharpening

            # Slight contrast enhancement for better text separation
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.05)  # Very subtle contrast boost

            self.logger.debug("Applied subtle text clarity enhancements")
            return image

        except Exception as e:
            self.logger.debug(f"Could not apply text enhancements: {e}")
            return image  # Return original if enhancement fails
    
    def calculate_image_hash(self, image: Image.Image) -> str:
        """Calculate a hash of the image for caching purposes.
        
        Args:
            image: PIL Image to hash
            
        Returns:
            Hash string of the image
        """
        try:
            # Resize to small size for fast hashing
            hash_image = image.resize((32, 32), Image.Resampling.NEAREST)
            # Convert to bytes and hash
            image_bytes = hash_image.tobytes()
            return hashlib.md5(image_bytes).hexdigest()
        except Exception as e:
            self.logger.error(f"Error calculating image hash: {e}")
            return str(time.time())  # Fallback to timestamp
    
    def check_cache(self, image_hash: str) -> Optional[Dict[str, Any]]:
        """Check if we have cached OCR results for this image.
        
        Args:
            image_hash: Hash of the image
            
        Returns:
            Cached OCR result or None
        """
        if image_hash == self._last_image_hash and self._last_ocr_result:
            self.logger.debug("Image identical to previous capture, reusing OCR result")
            return self._last_ocr_result
        return None
    
    def cache_result(self, image_hash: str, ocr_result: Dict[str, Any]) -> None:
        """Cache OCR result for future use.
        
        Args:
            image_hash: Hash of the image
            ocr_result: OCR result to cache
        """
        self._last_image_hash = image_hash
        self._last_ocr_result = ocr_result
    
    def is_image_too_small(self, image: Image.Image) -> bool:
        """Check if image is too small for meaningful OCR.

        Args:
            image: PIL Image to check

        Returns:
            True if image is too small
        """
        return image.size[0] < 300 or image.size[1] < 150  # Slightly higher threshold to skip very small windows

    def should_skip_ocr(self, image: Image.Image) -> bool:
        """Check if OCR should be skipped for performance reasons.

        Args:
            image: PIL Image to check

        Returns:
            True if OCR should be skipped
        """
        # Skip very large images that would be slow to process
        if max(image.size) > 2000:  # Reduced threshold for better performance
            return True

        # Skip very small images that won't have useful text
        if self.is_image_too_small(image):
            return True

        # Skip images with extreme aspect ratios (likely UI elements)
        width, height = image.size
        aspect_ratio = max(width, height) / min(width, height)
        if aspect_ratio > 10:  # Very wide or very tall images
            return True

        # Skip images that are likely to be text-heavy terminals/editors
        total_pixels = width * height
        if total_pixels > 1500000:  # ~1.5MP threshold for text-heavy content
            return True

        return False

    def optimize_for_terminal_content(self, image: Image.Image) -> Image.Image:
        """Optimize image specifically for terminal/editor content with lots of text.

        Args:
            image: Original PIL Image

        Returns:
            Optimized PIL Image focused on key areas
        """
        try:
            width, height = image.size

            # For very wide images (like full-screen terminals), focus on top portion
            if width > height * 2:  # Wide aspect ratio
                # Take top 40% of the image where important info usually is
                crop_height = int(height * 0.4)
                image = image.crop((0, 0, width, crop_height))
                self.logger.debug(f"Cropped wide terminal image to top portion: {image.size}")

            # For very tall images (like long terminal output), focus on top and bottom
            elif height > width * 2:  # Tall aspect ratio
                # Take top 30% where current command/output usually is
                crop_height = int(height * 0.3)
                image = image.crop((0, 0, width, crop_height))
                self.logger.debug(f"Cropped tall terminal image to top portion: {image.size}")

            # Aggressive resizing for terminal content (text is usually large enough)
            max_dimension = 800  # Much smaller for terminal content
            if max(image.size) > max_dimension:
                scale = max_dimension / max(image.size)
                new_width = int(image.width * scale)
                new_height = int(image.height * scale)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                self.logger.debug(f"Aggressively resized terminal content: {image.size}")

            return image

        except Exception as e:
            self.logger.error(f"Error optimizing terminal content: {e}")
            return image
    
    def clear_cache(self) -> None:
        """Clear the image cache."""
        self._last_image_hash = None
        self._last_ocr_result = None
        self.logger.debug("Image processor cache cleared")
