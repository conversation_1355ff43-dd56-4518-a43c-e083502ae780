"""
Storage manager for One.Whispr voice models.

This module handles model storage directory management,
path resolution, and storage operations.
"""

import os
import sys
import logging
import shutil
from pathlib import Path
from typing import Optional, List, Dict, Any

logger = logging.getLogger("whispr.models.storage")


class StorageManager:
    """Manages storage operations for voice models."""
    
    def __init__(self, base_path: Optional[str] = None):
        """Initialize the storage manager.
        
        Args:
            base_path: Optional custom base path for model storage.
                      If None, will use default paths based on environment.
        """
        self.logger = logging.getLogger("whispr.models.StorageManager")
        self._base_path = base_path
        self._storage_root = self._get_storage_root()

        # Log the detected configuration for debugging
        self.logger.info(f"Voice models storage configuration:")
        self.logger.info(f"  Development mode: {self._is_development_mode()}")
        self.logger.info(f"  Storage root: {self._storage_root}")
        self.logger.info(f"  App root: {self._get_app_root()}")

        self._ensure_storage_directories()
    
    def _get_storage_root(self) -> Path:
        """Get the root storage directory for models."""
        if self._base_path:
            return Path(self._base_path)

        # Use environment variable if set (passed from Electron main process)
        if 'WHISPR_VOICEMODELS_PATH' in os.environ:
            return Path(os.environ['WHISPR_VOICEMODELS_PATH'])

        # Fallback: detect based on executable location and development mode
        if self._is_development_mode():
            # Development mode: one.whispr-app/.dist/voicemodels
            return self._get_app_root() / ".dist" / "voicemodels"
        else:
            # Packaged mode: resources/voicemodels
            return self._get_app_root() / "resources" / "voicemodels"

    def _is_development_mode(self) -> bool:
        """Check if we're running in development mode."""
        # Check for environment variable first (most reliable)
        if 'WHISPR_IS_DEV' in os.environ:
            return os.environ['WHISPR_IS_DEV'].lower() in ('true', '1', 'yes')

        # Check if we're running from source (look for package.json in app root)
        app_root = self._get_app_root()
        if (app_root / "package.json").exists():
            return True

        # Check if we're in a PyInstaller bundle
        if hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS'):
            return False

        # Check for development indicators in the path
        current_file = Path(__file__)
        for parent in current_file.parents:
            # Look for development structure indicators
            if (parent / "python" / "main.py").exists() and (parent / "package.json").exists():
                return True

        return False

    def _get_app_root(self) -> Path:
        """Get the application root directory."""
        # If we're in a PyInstaller bundle, use the bundle directory
        if hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS'):
            # In packaged mode, go up from the executable location
            # Executable is typically in resources/backend/
            exe_path = Path(sys.executable)
            # Go up from backend to resources, then to app root
            return exe_path.parent.parent.parent

        # In development mode, find the one.whispr-app directory
        current_file = Path(__file__)
        for parent in current_file.parents:
            # Look for the app root by finding package.json with "one.whispr-app" structure
            if (parent / "package.json").exists() and (parent / "python" / "main.py").exists():
                return parent

        # Fallback: assume we're in the python subdirectory
        return current_file.parent.parent.parent.parent.parent
    
    def _ensure_storage_directories(self):
        """Ensure all required storage directories exist."""
        try:
            # Create main storage directory
            self._storage_root.mkdir(parents=True, exist_ok=True)
            
            # No longer creating provider subdirectories - models go directly in voicemodels
            self.logger.info(f"Storage directories initialized at: {self._storage_root}")
            
        except Exception as e:
            self.logger.error(f"Failed to create storage directories: {e}")
            raise
    
    def get_storage_root(self) -> Path:
        """Get the root storage directory."""
        return self._storage_root
    
    def get_model_directory(self, model_id: str) -> Path:
        """Get the storage directory for a specific model.
        
        Args:
            model_id: Model ID (e.g., "openai/whisper-tiny")
            
        Returns:
            Path to the model's storage directory
        """
        # Extract just the model name part (after the slash)
        # "openai/whisper-tiny" -> "whisper-tiny"
        # "distil-whisper/distil-small.en" -> "distil-small.en"
        if "/" in model_id:
            model_name = model_id.split("/", 1)[1]  # Take everything after first slash
        else:
            model_name = model_id  # Fallback if no slash
        
        return self._storage_root / model_name
    
    def create_model_directory(self, model_id: str) -> Path:
        """Create storage directory for a model.
        
        Args:
            model_id: Model ID (e.g., "openai/whisper-tiny")
            
        Returns:
            Path to the created model directory
        """
        model_dir = self.get_model_directory(model_id)
        try:
            model_dir.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"Created model directory: {model_dir}")
            return model_dir
        except Exception as e:
            self.logger.error(f"Failed to create model directory {model_dir}: {e}")
            raise
    
    def model_exists(self, model_id: str) -> bool:
        """Check if a model directory exists.
        
        Args:
            model_id: Model ID to check
            
        Returns:
            True if model directory exists
        """
        model_dir = self.get_model_directory(model_id)
        return model_dir.exists() and model_dir.is_dir()
    
    def get_model_files(self, model_id: str) -> List[str]:
        """Get list of files in a model directory.
        
        Args:
            model_id: Model ID
            
        Returns:
            List of filenames in the model directory
        """
        model_dir = self.get_model_directory(model_id)
        if not model_dir.exists():
            return []
        
        try:
            return [f.name for f in model_dir.iterdir() if f.is_file()]
        except Exception as e:
            self.logger.error(f"Error listing files for model {model_id}: {e}")
            return []
    
    def get_file_path(self, model_id: str, filename: str) -> Path:
        """Get the full path to a model file.
        
        Args:
            model_id: Model ID
            filename: Name of the file
            
        Returns:
            Full path to the file
        """
        model_dir = self.get_model_directory(model_id)
        return model_dir / filename
    
    def file_exists(self, model_id: str, filename: str) -> bool:
        """Check if a specific model file exists.
        
        Args:
            model_id: Model ID
            filename: Name of the file to check
            
        Returns:
            True if file exists
        """
        file_path = self.get_file_path(model_id, filename)
        return file_path.exists() and file_path.is_file()
    
    def get_file_size(self, model_id: str, filename: str) -> Optional[int]:
        """Get the size of a model file.
        
        Args:
            model_id: Model ID
            filename: Name of the file
            
        Returns:
            File size in bytes, or None if file doesn't exist
        """
        file_path = self.get_file_path(model_id, filename)
        try:
            if file_path.exists():
                return file_path.stat().st_size
        except Exception as e:
            self.logger.error(f"Error getting file size for {file_path}: {e}")
        return None
    
    def delete_model(self, model_id: str) -> bool:
        """Delete a model and all its files.
        
        Args:
            model_id: Model ID to delete
            
        Returns:
            True if deletion was successful
        """
        model_dir = self.get_model_directory(model_id)
        if not model_dir.exists():
            self.logger.warning(f"Model directory does not exist: {model_dir}")
            return True
        
        try:
            shutil.rmtree(model_dir)
            self.logger.info(f"Deleted model directory: {model_dir}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to delete model directory {model_dir}: {e}")
            return False
    
    def delete_file(self, model_id: str, filename: str) -> bool:
        """Delete a specific model file.
        
        Args:
            model_id: Model ID
            filename: Name of the file to delete
            
        Returns:
            True if deletion was successful
        """
        file_path = self.get_file_path(model_id, filename)
        if not file_path.exists():
            self.logger.warning(f"File does not exist: {file_path}")
            return True
        
        try:
            file_path.unlink()
            self.logger.debug(f"Deleted file: {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    def get_storage_info(self) -> Dict[str, Any]:
        """Get information about storage usage.
        
        Returns:
            Dictionary with storage information
        """
        try:
            # Get total size of storage directory
            total_size = 0
            file_count = 0
            model_count = 0
            
            if self._storage_root.exists():
                for root, dirs, files in os.walk(self._storage_root):
                    root_path = Path(root)
                    
                    # Count models (directories directly under storage root)
                    if root_path == self._storage_root and dirs:
                        model_count = len(dirs)
                    
                    # Count files and calculate total size
                    for file in files:
                        file_path = Path(root) / file
                        try:
                            total_size += file_path.stat().st_size
                            file_count += 1
                        except Exception:
                            pass
            
            # Get available disk space
            try:
                disk_usage = shutil.disk_usage(self._storage_root)
                available_space = disk_usage.free
            except Exception:
                available_space = None
            
            return {
                "storage_root": str(self._storage_root),
                "total_size": total_size,
                "file_count": file_count,
                "model_count": model_count,
                "available_space": available_space,
                "is_development": self._is_development_mode()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting storage info: {e}")
            return {
                "storage_root": str(self._storage_root),
                "error": str(e)
            }
    
    def cleanup_empty_directories(self):
        """Remove empty model directories."""
        try:
            # Check model directories directly under storage root
            for model_dir in self._storage_root.iterdir():
                if model_dir.is_dir() and not any(model_dir.iterdir()):
                    model_dir.rmdir()
                    self.logger.debug(f"Removed empty directory: {model_dir}")
                        
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}") 